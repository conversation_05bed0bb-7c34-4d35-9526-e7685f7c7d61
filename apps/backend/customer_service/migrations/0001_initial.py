# Generated by Django 4.2.9 on 2025-06-05 08:54

from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import simple_history.models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("clients", "0003_alter_client_total_revenue_currency_and_more"),
        ("projects", "0003_alter_historicalproject_actual_cost_currency_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="Ticket",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "ticket_number",
                    models.CharField(
                        max_length=20, unique=True, verbose_name="رقم التذكرة"
                    ),
                ),
                ("title", models.CharField(max_length=200, verbose_name="العنوان")),
                ("description", models.TextField(verbose_name="الوصف")),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("open", "مفتوح"),
                            ("in_progress", "قيد المعالجة"),
                            ("pending_client", "في انتظار العميل"),
                            ("resolved", "تم الحل"),
                            ("closed", "مغلق"),
                            ("escalated", "تم التصعيد"),
                        ],
                        default="open",
                        max_length=20,
                        verbose_name="الحالة",
                    ),
                ),
                (
                    "priority",
                    models.CharField(
                        choices=[
                            ("critical", "حرج"),
                            ("high", "عالي"),
                            ("medium", "متوسط"),
                            ("low", "منخفض"),
                        ],
                        default="medium",
                        max_length=10,
                        verbose_name="الأولوية",
                    ),
                ),
                (
                    "category",
                    models.CharField(
                        choices=[
                            ("technical", "تقني"),
                            ("billing", "الفواتير"),
                            ("general", "عام"),
                            ("project_related", "متعلق بالمشروع"),
                            ("account", "الحساب"),
                            ("feature_request", "طلب ميزة"),
                            ("bug_report", "تقرير خطأ"),
                        ],
                        default="general",
                        max_length=20,
                        verbose_name="الفئة",
                    ),
                ),
                (
                    "source",
                    models.CharField(
                        choices=[
                            ("email", "بريد إلكتروني"),
                            ("phone", "هاتف"),
                            ("whatsapp", "واتساب"),
                            ("web_portal", "البوابة الإلكترونية"),
                            ("internal", "داخلي"),
                        ],
                        default="web_portal",
                        max_length=15,
                        verbose_name="المصدر",
                    ),
                ),
                (
                    "sla_due_date",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="موعد SLA"
                    ),
                ),
                (
                    "escalation_level",
                    models.PositiveIntegerField(
                        default=1, verbose_name="مستوى التصعيد"
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True, verbose_name="تاريخ الإنشاء"
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث"),
                ),
                (
                    "resolved_at",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="تاريخ الحل"
                    ),
                ),
                (
                    "closed_at",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="تاريخ الإغلاق"
                    ),
                ),
                (
                    "satisfaction_rating",
                    models.PositiveIntegerField(
                        blank=True,
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(5),
                        ],
                        verbose_name="تقييم الرضا",
                    ),
                ),
                (
                    "satisfaction_feedback",
                    models.TextField(blank=True, null=True, verbose_name="تعليق الرضا"),
                ),
                (
                    "attachments",
                    models.JSONField(blank=True, default=list, verbose_name="المرفقات"),
                ),
                (
                    "metadata",
                    models.JSONField(
                        blank=True, default=dict, verbose_name="بيانات إضافية"
                    ),
                ),
                (
                    "assigned_to",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="assigned_tickets",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="مكلف إلى",
                    ),
                ),
                (
                    "client",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="support_tickets",
                        to="clients.client",
                        verbose_name="العميل",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="created_tickets",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="أنشأ بواسطة",
                    ),
                ),
                (
                    "project",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="support_tickets",
                        to="projects.project",
                        verbose_name="المشروع",
                    ),
                ),
            ],
            options={
                "verbose_name": "تذكرة دعم",
                "verbose_name_plural": "تذاكر الدعم",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="TicketTemplate",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100, verbose_name="اسم القالب")),
                ("description", models.TextField(blank=True, verbose_name="الوصف")),
                (
                    "template_type",
                    models.CharField(
                        choices=[
                            ("response", "رد"),
                            ("resolution", "حل"),
                            ("escalation", "تصعيد"),
                            ("closure", "إغلاق"),
                        ],
                        max_length=15,
                        verbose_name="نوع القالب",
                    ),
                ),
                (
                    "subject_template",
                    models.CharField(
                        blank=True, max_length=200, verbose_name="قالب الموضوع"
                    ),
                ),
                ("body_template", models.TextField(verbose_name="قالب المحتوى")),
                (
                    "category",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("technical", "تقني"),
                            ("billing", "الفواتير"),
                            ("general", "عام"),
                            ("project_related", "متعلق بالمشروع"),
                            ("account", "الحساب"),
                            ("feature_request", "طلب ميزة"),
                            ("bug_report", "تقرير خطأ"),
                        ],
                        max_length=20,
                        verbose_name="الفئة",
                    ),
                ),
                ("is_active", models.BooleanField(default=True, verbose_name="نشط")),
                (
                    "usage_count",
                    models.PositiveIntegerField(
                        default=0, verbose_name="عدد الاستخدامات"
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True, verbose_name="تاريخ الإنشاء"
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث"),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="أنشأ بواسطة",
                    ),
                ),
            ],
            options={
                "verbose_name": "قالب التذكرة",
                "verbose_name_plural": "قوالب التذاكر",
                "ordering": ["template_type", "name"],
            },
        ),
        migrations.CreateModel(
            name="TicketEscalation",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "reason",
                    models.CharField(
                        choices=[
                            ("sla_breach", "انتهاك SLA"),
                            ("client_request", "طلب العميل"),
                            ("complexity", "تعقيد"),
                            ("manual", "يدوي"),
                        ],
                        max_length=20,
                        verbose_name="سبب التصعيد",
                    ),
                ),
                ("notes", models.TextField(blank=True, verbose_name="ملاحظات")),
                (
                    "escalation_level",
                    models.PositiveIntegerField(verbose_name="مستوى التصعيد"),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True, verbose_name="تاريخ التصعيد"
                    ),
                ),
                (
                    "escalated_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="escalated_by_tickets",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="تم التصعيد بواسطة",
                    ),
                ),
                (
                    "escalated_from",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="escalated_from_tickets",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="تم التصعيد من",
                    ),
                ),
                (
                    "escalated_to",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="escalated_to_tickets",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="تم التصعيد إلى",
                    ),
                ),
                (
                    "ticket",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="escalations",
                        to="customer_service.ticket",
                        verbose_name="التذكرة",
                    ),
                ),
            ],
            options={
                "verbose_name": "تصعيد التذكرة",
                "verbose_name_plural": "تصعيدات التذاكر",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="SLARule",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100, verbose_name="اسم القاعدة")),
                (
                    "priority",
                    models.CharField(
                        choices=[
                            ("critical", "حرج"),
                            ("high", "عالي"),
                            ("medium", "متوسط"),
                            ("low", "منخفض"),
                        ],
                        max_length=10,
                        verbose_name="الأولوية",
                    ),
                ),
                (
                    "client_tier",
                    models.CharField(
                        choices=[
                            ("vip", "VIP"),
                            ("premium", "مميز"),
                            ("standard", "عادي"),
                            ("basic", "أساسي"),
                        ],
                        default="standard",
                        max_length=10,
                        verbose_name="فئة العميل",
                    ),
                ),
                (
                    "response_time_hours",
                    models.PositiveIntegerField(verbose_name="وقت الاستجابة (ساعات)"),
                ),
                (
                    "resolution_time_hours",
                    models.PositiveIntegerField(verbose_name="وقت الحل (ساعات)"),
                ),
                (
                    "escalation_time_hours",
                    models.PositiveIntegerField(verbose_name="وقت التصعيد (ساعات)"),
                ),
                (
                    "business_hours_only",
                    models.BooleanField(default=True, verbose_name="ساعات العمل فقط"),
                ),
                ("is_active", models.BooleanField(default=True, verbose_name="نشط")),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True, verbose_name="تاريخ الإنشاء"
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث"),
                ),
            ],
            options={
                "verbose_name": "قاعدة SLA",
                "verbose_name_plural": "قواعد SLA",
                "ordering": ["priority", "client_tier"],
                "unique_together": {("priority", "client_tier")},
            },
        ),
        migrations.CreateModel(
            name="KnowledgeBase",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "title_ar",
                    models.CharField(max_length=200, verbose_name="العنوان بالعربية"),
                ),
                (
                    "title_en",
                    models.CharField(
                        blank=True, max_length=200, verbose_name="العنوان بالإنجليزية"
                    ),
                ),
                ("content_ar", models.TextField(verbose_name="المحتوى بالعربية")),
                (
                    "content_en",
                    models.TextField(blank=True, verbose_name="المحتوى بالإنجليزية"),
                ),
                (
                    "category",
                    models.CharField(
                        choices=[
                            ("general", "عام"),
                            ("technical", "تقني"),
                            ("billing", "الفواتير"),
                            ("projects", "المشاريع"),
                            ("account", "الحساب"),
                            ("troubleshooting", "استكشاف الأخطاء"),
                        ],
                        default="general",
                        max_length=20,
                        verbose_name="الفئة",
                    ),
                ),
                (
                    "tags",
                    models.JSONField(blank=True, default=list, verbose_name="العلامات"),
                ),
                ("is_public", models.BooleanField(default=True, verbose_name="عام")),
                (
                    "is_featured",
                    models.BooleanField(default=False, verbose_name="مميز"),
                ),
                (
                    "view_count",
                    models.PositiveIntegerField(
                        default=0, verbose_name="عدد المشاهدات"
                    ),
                ),
                (
                    "helpful_count",
                    models.PositiveIntegerField(
                        default=0, verbose_name="عدد التقييمات المفيدة"
                    ),
                ),
                (
                    "not_helpful_count",
                    models.PositiveIntegerField(
                        default=0, verbose_name="عدد التقييمات غير المفيدة"
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True, verbose_name="تاريخ الإنشاء"
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث"),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="knowledge_articles",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="أنشأ بواسطة",
                    ),
                ),
            ],
            options={
                "verbose_name": "مقال قاعدة المعرفة",
                "verbose_name_plural": "مقالات قاعدة المعرفة",
                "ordering": ["-is_featured", "-created_at"],
            },
        ),
        migrations.CreateModel(
            name="HistoricalTicketTemplate",
            fields=[
                (
                    "id",
                    models.BigIntegerField(
                        auto_created=True, blank=True, db_index=True, verbose_name="ID"
                    ),
                ),
                ("name", models.CharField(max_length=100, verbose_name="اسم القالب")),
                ("description", models.TextField(blank=True, verbose_name="الوصف")),
                (
                    "template_type",
                    models.CharField(
                        choices=[
                            ("response", "رد"),
                            ("resolution", "حل"),
                            ("escalation", "تصعيد"),
                            ("closure", "إغلاق"),
                        ],
                        max_length=15,
                        verbose_name="نوع القالب",
                    ),
                ),
                (
                    "subject_template",
                    models.CharField(
                        blank=True, max_length=200, verbose_name="قالب الموضوع"
                    ),
                ),
                ("body_template", models.TextField(verbose_name="قالب المحتوى")),
                (
                    "category",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("technical", "تقني"),
                            ("billing", "الفواتير"),
                            ("general", "عام"),
                            ("project_related", "متعلق بالمشروع"),
                            ("account", "الحساب"),
                            ("feature_request", "طلب ميزة"),
                            ("bug_report", "تقرير خطأ"),
                        ],
                        max_length=20,
                        verbose_name="الفئة",
                    ),
                ),
                ("is_active", models.BooleanField(default=True, verbose_name="نشط")),
                (
                    "usage_count",
                    models.PositiveIntegerField(
                        default=0, verbose_name="عدد الاستخدامات"
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        blank=True, editable=False, verbose_name="تاريخ الإنشاء"
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(
                        blank=True, editable=False, verbose_name="تاريخ التحديث"
                    ),
                ),
                ("history_id", models.AutoField(primary_key=True, serialize=False)),
                ("history_date", models.DateTimeField(db_index=True)),
                ("history_change_reason", models.CharField(max_length=100, null=True)),
                (
                    "history_type",
                    models.CharField(
                        choices=[("+", "Created"), ("~", "Changed"), ("-", "Deleted")],
                        max_length=1,
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="أنشأ بواسطة",
                    ),
                ),
                (
                    "history_user",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="+",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "historical قالب التذكرة",
                "verbose_name_plural": "historical قوالب التذاكر",
                "ordering": ("-history_date", "-history_id"),
                "get_latest_by": ("history_date", "history_id"),
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
        migrations.CreateModel(
            name="HistoricalTicketResponse",
            fields=[
                (
                    "id",
                    models.BigIntegerField(
                        auto_created=True, blank=True, db_index=True, verbose_name="ID"
                    ),
                ),
                ("response_text", models.TextField(verbose_name="نص الرد")),
                (
                    "response_type",
                    models.CharField(
                        choices=[
                            ("comment", "تعليق"),
                            ("status_change", "تغيير الحالة"),
                            ("assignment", "تكليف"),
                            ("escalation", "تصعيد"),
                            ("resolution", "حل"),
                            ("client_feedback", "تعليق العميل"),
                        ],
                        default="comment",
                        max_length=20,
                        verbose_name="نوع الرد",
                    ),
                ),
                (
                    "is_internal",
                    models.BooleanField(default=False, verbose_name="داخلي"),
                ),
                (
                    "attachments",
                    models.JSONField(blank=True, default=list, verbose_name="المرفقات"),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        blank=True, editable=False, verbose_name="تاريخ الإنشاء"
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(
                        blank=True, editable=False, verbose_name="تاريخ التحديث"
                    ),
                ),
                ("history_id", models.AutoField(primary_key=True, serialize=False)),
                ("history_date", models.DateTimeField(db_index=True)),
                ("history_change_reason", models.CharField(max_length=100, null=True)),
                (
                    "history_type",
                    models.CharField(
                        choices=[("+", "Created"), ("~", "Changed"), ("-", "Deleted")],
                        max_length=1,
                    ),
                ),
                (
                    "history_user",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="+",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "ticket",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to="customer_service.ticket",
                        verbose_name="التذكرة",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="المستخدم",
                    ),
                ),
            ],
            options={
                "verbose_name": "historical رد التذكرة",
                "verbose_name_plural": "historical ردود التذاكر",
                "ordering": ("-history_date", "-history_id"),
                "get_latest_by": ("history_date", "history_id"),
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
        migrations.CreateModel(
            name="HistoricalTicketEscalation",
            fields=[
                (
                    "id",
                    models.BigIntegerField(
                        auto_created=True, blank=True, db_index=True, verbose_name="ID"
                    ),
                ),
                (
                    "reason",
                    models.CharField(
                        choices=[
                            ("sla_breach", "انتهاك SLA"),
                            ("client_request", "طلب العميل"),
                            ("complexity", "تعقيد"),
                            ("manual", "يدوي"),
                        ],
                        max_length=20,
                        verbose_name="سبب التصعيد",
                    ),
                ),
                ("notes", models.TextField(blank=True, verbose_name="ملاحظات")),
                (
                    "escalation_level",
                    models.PositiveIntegerField(verbose_name="مستوى التصعيد"),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        blank=True, editable=False, verbose_name="تاريخ التصعيد"
                    ),
                ),
                ("history_id", models.AutoField(primary_key=True, serialize=False)),
                ("history_date", models.DateTimeField(db_index=True)),
                ("history_change_reason", models.CharField(max_length=100, null=True)),
                (
                    "history_type",
                    models.CharField(
                        choices=[("+", "Created"), ("~", "Changed"), ("-", "Deleted")],
                        max_length=1,
                    ),
                ),
                (
                    "escalated_by",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="تم التصعيد بواسطة",
                    ),
                ),
                (
                    "escalated_from",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="تم التصعيد من",
                    ),
                ),
                (
                    "escalated_to",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="تم التصعيد إلى",
                    ),
                ),
                (
                    "history_user",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="+",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "ticket",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to="customer_service.ticket",
                        verbose_name="التذكرة",
                    ),
                ),
            ],
            options={
                "verbose_name": "historical تصعيد التذكرة",
                "verbose_name_plural": "historical تصعيدات التذاكر",
                "ordering": ("-history_date", "-history_id"),
                "get_latest_by": ("history_date", "history_id"),
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
        migrations.CreateModel(
            name="HistoricalTicket",
            fields=[
                (
                    "id",
                    models.BigIntegerField(
                        auto_created=True, blank=True, db_index=True, verbose_name="ID"
                    ),
                ),
                (
                    "ticket_number",
                    models.CharField(
                        db_index=True, max_length=20, verbose_name="رقم التذكرة"
                    ),
                ),
                ("title", models.CharField(max_length=200, verbose_name="العنوان")),
                ("description", models.TextField(verbose_name="الوصف")),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("open", "مفتوح"),
                            ("in_progress", "قيد المعالجة"),
                            ("pending_client", "في انتظار العميل"),
                            ("resolved", "تم الحل"),
                            ("closed", "مغلق"),
                            ("escalated", "تم التصعيد"),
                        ],
                        default="open",
                        max_length=20,
                        verbose_name="الحالة",
                    ),
                ),
                (
                    "priority",
                    models.CharField(
                        choices=[
                            ("critical", "حرج"),
                            ("high", "عالي"),
                            ("medium", "متوسط"),
                            ("low", "منخفض"),
                        ],
                        default="medium",
                        max_length=10,
                        verbose_name="الأولوية",
                    ),
                ),
                (
                    "category",
                    models.CharField(
                        choices=[
                            ("technical", "تقني"),
                            ("billing", "الفواتير"),
                            ("general", "عام"),
                            ("project_related", "متعلق بالمشروع"),
                            ("account", "الحساب"),
                            ("feature_request", "طلب ميزة"),
                            ("bug_report", "تقرير خطأ"),
                        ],
                        default="general",
                        max_length=20,
                        verbose_name="الفئة",
                    ),
                ),
                (
                    "source",
                    models.CharField(
                        choices=[
                            ("email", "بريد إلكتروني"),
                            ("phone", "هاتف"),
                            ("whatsapp", "واتساب"),
                            ("web_portal", "البوابة الإلكترونية"),
                            ("internal", "داخلي"),
                        ],
                        default="web_portal",
                        max_length=15,
                        verbose_name="المصدر",
                    ),
                ),
                (
                    "sla_due_date",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="موعد SLA"
                    ),
                ),
                (
                    "escalation_level",
                    models.PositiveIntegerField(
                        default=1, verbose_name="مستوى التصعيد"
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        blank=True, editable=False, verbose_name="تاريخ الإنشاء"
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(
                        blank=True, editable=False, verbose_name="تاريخ التحديث"
                    ),
                ),
                (
                    "resolved_at",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="تاريخ الحل"
                    ),
                ),
                (
                    "closed_at",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="تاريخ الإغلاق"
                    ),
                ),
                (
                    "satisfaction_rating",
                    models.PositiveIntegerField(
                        blank=True,
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(5),
                        ],
                        verbose_name="تقييم الرضا",
                    ),
                ),
                (
                    "satisfaction_feedback",
                    models.TextField(blank=True, null=True, verbose_name="تعليق الرضا"),
                ),
                (
                    "attachments",
                    models.JSONField(blank=True, default=list, verbose_name="المرفقات"),
                ),
                (
                    "metadata",
                    models.JSONField(
                        blank=True, default=dict, verbose_name="بيانات إضافية"
                    ),
                ),
                ("history_id", models.AutoField(primary_key=True, serialize=False)),
                ("history_date", models.DateTimeField(db_index=True)),
                ("history_change_reason", models.CharField(max_length=100, null=True)),
                (
                    "history_type",
                    models.CharField(
                        choices=[("+", "Created"), ("~", "Changed"), ("-", "Deleted")],
                        max_length=1,
                    ),
                ),
                (
                    "assigned_to",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="مكلف إلى",
                    ),
                ),
                (
                    "client",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to="clients.client",
                        verbose_name="العميل",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="أنشأ بواسطة",
                    ),
                ),
                (
                    "history_user",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="+",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "project",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to="projects.project",
                        verbose_name="المشروع",
                    ),
                ),
            ],
            options={
                "verbose_name": "historical تذكرة دعم",
                "verbose_name_plural": "historical تذاكر الدعم",
                "ordering": ("-history_date", "-history_id"),
                "get_latest_by": ("history_date", "history_id"),
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
        migrations.CreateModel(
            name="HistoricalSLARule",
            fields=[
                (
                    "id",
                    models.BigIntegerField(
                        auto_created=True, blank=True, db_index=True, verbose_name="ID"
                    ),
                ),
                ("name", models.CharField(max_length=100, verbose_name="اسم القاعدة")),
                (
                    "priority",
                    models.CharField(
                        choices=[
                            ("critical", "حرج"),
                            ("high", "عالي"),
                            ("medium", "متوسط"),
                            ("low", "منخفض"),
                        ],
                        max_length=10,
                        verbose_name="الأولوية",
                    ),
                ),
                (
                    "client_tier",
                    models.CharField(
                        choices=[
                            ("vip", "VIP"),
                            ("premium", "مميز"),
                            ("standard", "عادي"),
                            ("basic", "أساسي"),
                        ],
                        default="standard",
                        max_length=10,
                        verbose_name="فئة العميل",
                    ),
                ),
                (
                    "response_time_hours",
                    models.PositiveIntegerField(verbose_name="وقت الاستجابة (ساعات)"),
                ),
                (
                    "resolution_time_hours",
                    models.PositiveIntegerField(verbose_name="وقت الحل (ساعات)"),
                ),
                (
                    "escalation_time_hours",
                    models.PositiveIntegerField(verbose_name="وقت التصعيد (ساعات)"),
                ),
                (
                    "business_hours_only",
                    models.BooleanField(default=True, verbose_name="ساعات العمل فقط"),
                ),
                ("is_active", models.BooleanField(default=True, verbose_name="نشط")),
                (
                    "created_at",
                    models.DateTimeField(
                        blank=True, editable=False, verbose_name="تاريخ الإنشاء"
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(
                        blank=True, editable=False, verbose_name="تاريخ التحديث"
                    ),
                ),
                ("history_id", models.AutoField(primary_key=True, serialize=False)),
                ("history_date", models.DateTimeField(db_index=True)),
                ("history_change_reason", models.CharField(max_length=100, null=True)),
                (
                    "history_type",
                    models.CharField(
                        choices=[("+", "Created"), ("~", "Changed"), ("-", "Deleted")],
                        max_length=1,
                    ),
                ),
                (
                    "history_user",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="+",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "historical قاعدة SLA",
                "verbose_name_plural": "historical قواعد SLA",
                "ordering": ("-history_date", "-history_id"),
                "get_latest_by": ("history_date", "history_id"),
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
        migrations.CreateModel(
            name="HistoricalKnowledgeBase",
            fields=[
                (
                    "id",
                    models.BigIntegerField(
                        auto_created=True, blank=True, db_index=True, verbose_name="ID"
                    ),
                ),
                (
                    "title_ar",
                    models.CharField(max_length=200, verbose_name="العنوان بالعربية"),
                ),
                (
                    "title_en",
                    models.CharField(
                        blank=True, max_length=200, verbose_name="العنوان بالإنجليزية"
                    ),
                ),
                ("content_ar", models.TextField(verbose_name="المحتوى بالعربية")),
                (
                    "content_en",
                    models.TextField(blank=True, verbose_name="المحتوى بالإنجليزية"),
                ),
                (
                    "category",
                    models.CharField(
                        choices=[
                            ("general", "عام"),
                            ("technical", "تقني"),
                            ("billing", "الفواتير"),
                            ("projects", "المشاريع"),
                            ("account", "الحساب"),
                            ("troubleshooting", "استكشاف الأخطاء"),
                        ],
                        default="general",
                        max_length=20,
                        verbose_name="الفئة",
                    ),
                ),
                (
                    "tags",
                    models.JSONField(blank=True, default=list, verbose_name="العلامات"),
                ),
                ("is_public", models.BooleanField(default=True, verbose_name="عام")),
                (
                    "is_featured",
                    models.BooleanField(default=False, verbose_name="مميز"),
                ),
                (
                    "view_count",
                    models.PositiveIntegerField(
                        default=0, verbose_name="عدد المشاهدات"
                    ),
                ),
                (
                    "helpful_count",
                    models.PositiveIntegerField(
                        default=0, verbose_name="عدد التقييمات المفيدة"
                    ),
                ),
                (
                    "not_helpful_count",
                    models.PositiveIntegerField(
                        default=0, verbose_name="عدد التقييمات غير المفيدة"
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        blank=True, editable=False, verbose_name="تاريخ الإنشاء"
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(
                        blank=True, editable=False, verbose_name="تاريخ التحديث"
                    ),
                ),
                ("history_id", models.AutoField(primary_key=True, serialize=False)),
                ("history_date", models.DateTimeField(db_index=True)),
                ("history_change_reason", models.CharField(max_length=100, null=True)),
                (
                    "history_type",
                    models.CharField(
                        choices=[("+", "Created"), ("~", "Changed"), ("-", "Deleted")],
                        max_length=1,
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="أنشأ بواسطة",
                    ),
                ),
                (
                    "history_user",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="+",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "historical مقال قاعدة المعرفة",
                "verbose_name_plural": "historical مقالات قاعدة المعرفة",
                "ordering": ("-history_date", "-history_id"),
                "get_latest_by": ("history_date", "history_id"),
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
        migrations.CreateModel(
            name="TicketResponse",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("response_text", models.TextField(verbose_name="نص الرد")),
                (
                    "response_type",
                    models.CharField(
                        choices=[
                            ("comment", "تعليق"),
                            ("status_change", "تغيير الحالة"),
                            ("assignment", "تكليف"),
                            ("escalation", "تصعيد"),
                            ("resolution", "حل"),
                            ("client_feedback", "تعليق العميل"),
                        ],
                        default="comment",
                        max_length=20,
                        verbose_name="نوع الرد",
                    ),
                ),
                (
                    "is_internal",
                    models.BooleanField(default=False, verbose_name="داخلي"),
                ),
                (
                    "attachments",
                    models.JSONField(blank=True, default=list, verbose_name="المرفقات"),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True, verbose_name="تاريخ الإنشاء"
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث"),
                ),
                (
                    "ticket",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="responses",
                        to="customer_service.ticket",
                        verbose_name="التذكرة",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="ticket_responses",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="المستخدم",
                    ),
                ),
            ],
            options={
                "verbose_name": "رد التذكرة",
                "verbose_name_plural": "ردود التذاكر",
                "ordering": ["created_at"],
                "indexes": [
                    models.Index(
                        fields=["ticket", "created_at"],
                        name="response_ticket_created_idx",
                    ),
                    models.Index(
                        fields=["user", "created_at"], name="response_user_created_idx"
                    ),
                ],
            },
        ),
        migrations.AddIndex(
            model_name="ticket",
            index=models.Index(
                fields=["status", "priority"], name="ticket_status_priority_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="ticket",
            index=models.Index(
                fields=["client", "status"], name="ticket_client_status_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="ticket",
            index=models.Index(
                fields=["assigned_to", "status"], name="ticket_assigned_status_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="ticket",
            index=models.Index(fields=["sla_due_date"], name="ticket_sla_due_idx"),
        ),
        migrations.AddIndex(
            model_name="knowledgebase",
            index=models.Index(
                fields=["category", "is_public"], name="kb_category_public_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="knowledgebase",
            index=models.Index(
                fields=["is_featured", "is_public"], name="kb_featured_public_idx"
            ),
        ),
    ]
