"""
Create sample customer service data for testing
Following DRY, ULM, CPR, GRS, VST, SMA, CBR, BOP, API-First Mentality principles
"""

from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import timedelta
import random

from customer_service.models import (
    SLARule,
    KnowledgeBase,
    Ticket,
    TicketResponse,
    TicketTemplate
)
from clients.models import Client
from projects.models import Project

User = get_user_model()


class Command(BaseCommand):
    help = 'Create sample customer service data for testing'

    def handle(self, *args, **options):
        self.stdout.write('Creating sample customer service data...')

        # Create SLA Rules
        self.create_sla_rules()
        
        # Create Knowledge Base Articles
        self.create_knowledge_base_articles()
        
        # Create Ticket Templates
        self.create_ticket_templates()
        
        # Create Sample Tickets
        self.create_sample_tickets()

        self.stdout.write(
            self.style.SUCCESS('Successfully created sample customer service data!')
        )

    def create_sla_rules(self):
        """Create SLA rules for different priorities and client tiers"""
        sla_rules = [
            # VIP Client Rules
            {
                'name': 'VIP - حرج',
                'priority': 'critical',
                'client_tier': 'vip',
                'response_time_hours': 1,
                'resolution_time_hours': 4,
                'escalation_time_hours': 2,
            },
            {
                'name': 'VIP - عالي',
                'priority': 'high',
                'client_tier': 'vip',
                'response_time_hours': 2,
                'resolution_time_hours': 8,
                'escalation_time_hours': 4,
            },
            # Premium Client Rules
            {
                'name': 'مميز - حرج',
                'priority': 'critical',
                'client_tier': 'premium',
                'response_time_hours': 2,
                'resolution_time_hours': 8,
                'escalation_time_hours': 4,
            },
            {
                'name': 'مميز - عالي',
                'priority': 'high',
                'client_tier': 'premium',
                'response_time_hours': 4,
                'resolution_time_hours': 24,
                'escalation_time_hours': 8,
            },
            # Standard Client Rules
            {
                'name': 'عادي - حرج',
                'priority': 'critical',
                'client_tier': 'standard',
                'response_time_hours': 4,
                'resolution_time_hours': 24,
                'escalation_time_hours': 8,
            },
            {
                'name': 'عادي - عالي',
                'priority': 'high',
                'client_tier': 'standard',
                'response_time_hours': 8,
                'resolution_time_hours': 48,
                'escalation_time_hours': 16,
            },
            {
                'name': 'عادي - متوسط',
                'priority': 'medium',
                'client_tier': 'standard',
                'response_time_hours': 24,
                'resolution_time_hours': 72,
                'escalation_time_hours': 48,
            },
        ]

        for rule_data in sla_rules:
            SLARule.objects.get_or_create(
                priority=rule_data['priority'],
                client_tier=rule_data['client_tier'],
                defaults=rule_data
            )

        self.stdout.write('Created SLA rules')

    def create_knowledge_base_articles(self):
        """Create knowledge base articles"""
        articles = [
            {
                'title_ar': 'كيفية إنشاء مشروع جديد',
                'title_en': 'How to Create a New Project',
                'content_ar': 'لإنشاء مشروع جديد، اتبع الخطوات التالية:\n1. انتقل إلى قسم المشاريع\n2. اضغط على "مشروع جديد"\n3. املأ البيانات المطلوبة\n4. احفظ المشروع',
                'content_en': 'To create a new project, follow these steps:\n1. Go to Projects section\n2. Click "New Project"\n3. Fill required information\n4. Save the project',
                'category': 'general',
                'tags': ['مشاريع', 'إنشاء', 'دليل'],
                'is_featured': True,
            },
            {
                'title_ar': 'حل مشاكل تسجيل الدخول',
                'title_en': 'Login Issues Troubleshooting',
                'content_ar': 'إذا كنت تواجه مشاكل في تسجيل الدخول:\n1. تأكد من صحة البريد الإلكتروني وكلمة المرور\n2. امسح ذاكرة التخزين المؤقت للمتصفح\n3. جرب متصفح آخر\n4. تواصل مع الدعم الفني',
                'content_en': 'If you are experiencing login issues:\n1. Verify email and password\n2. Clear browser cache\n3. Try another browser\n4. Contact technical support',
                'category': 'technical',
                'tags': ['تسجيل دخول', 'مشاكل تقنية', 'حلول'],
                'is_featured': True,
            },
            {
                'title_ar': 'فهم الفواتير والمدفوعات',
                'title_en': 'Understanding Invoices and Payments',
                'content_ar': 'دليل شامل لفهم نظام الفواتير:\n1. أنواع الفواتير\n2. طرق الدفع المتاحة\n3. مواعيد الاستحقاق\n4. كيفية تحميل الفواتير',
                'content_en': 'Complete guide to understanding invoicing:\n1. Invoice types\n2. Available payment methods\n3. Due dates\n4. How to download invoices',
                'category': 'billing',
                'tags': ['فواتير', 'مدفوعات', 'محاسبة'],
                'is_featured': False,
            },
            {
                'title_ar': 'إدارة إعدادات الحساب',
                'title_en': 'Managing Account Settings',
                'content_ar': 'كيفية إدارة إعدادات حسابك:\n1. تحديث المعلومات الشخصية\n2. تغيير كلمة المرور\n3. إعدادات الإشعارات\n4. إعدادات الخصوصية',
                'content_en': 'How to manage your account settings:\n1. Update personal information\n2. Change password\n3. Notification settings\n4. Privacy settings',
                'category': 'account',
                'tags': ['حساب', 'إعدادات', 'أمان'],
                'is_featured': False,
            },
        ]

        admin_user = User.objects.filter(is_staff=True).first()
        
        for article_data in articles:
            article, created = KnowledgeBase.objects.get_or_create(
                title_ar=article_data['title_ar'],
                defaults={**article_data, 'created_by': admin_user}
            )
            if created:
                # Simulate some views and ratings
                article.view_count = random.randint(10, 100)
                article.helpful_count = random.randint(5, 20)
                article.not_helpful_count = random.randint(0, 5)
                article.save()

        self.stdout.write('Created knowledge base articles')

    def create_ticket_templates(self):
        """Create ticket templates"""
        templates = [
            {
                'name': 'رد ترحيبي',
                'description': 'رد ترحيبي للتذاكر الجديدة',
                'template_type': 'response',
                'subject_template': 'شكراً لتواصلك معنا - {ticket_number}',
                'body_template': 'عزيزي {client_name},\n\nشكراً لتواصلك معنا. تم استلام طلبك وسيتم الرد عليك في أقرب وقت ممكن.\n\nرقم التذكرة: {ticket_number}\n\nمع تحياتنا,\nفريق خدمة العملاء',
                'category': 'general',
            },
            {
                'name': 'حل مشكلة تقنية',
                'description': 'قالب لحل المشاكل التقنية',
                'template_type': 'resolution',
                'subject_template': 'تم حل المشكلة التقنية - {ticket_number}',
                'body_template': 'عزيزي {client_name},\n\nتم حل المشكلة التقنية التي أبلغت عنها. يرجى التحقق من النظام والتأكد من عمله بشكل صحيح.\n\nإذا استمرت المشكلة، يرجى التواصل معنا مرة أخرى.\n\nمع تحياتنا,\nالفريق التقني',
                'category': 'technical',
            },
            {
                'name': 'تصعيد للإدارة',
                'description': 'قالب تصعيد المشاكل للإدارة',
                'template_type': 'escalation',
                'subject_template': 'تصعيد تذكرة - {ticket_number}',
                'body_template': 'تم تصعيد التذكرة رقم {ticket_number} للإدارة نظراً لتعقيد المشكلة أو تجاوز الوقت المحدد للحل.\n\nتفاصيل التذكرة:\n{ticket_details}\n\nيرجى المتابعة العاجلة.',
                'category': 'general',
            },
        ]

        admin_user = User.objects.filter(is_staff=True).first()
        
        for template_data in templates:
            template, created = TicketTemplate.objects.get_or_create(
                name=template_data['name'],
                defaults={**template_data, 'created_by': admin_user}
            )
            if created:
                template.usage_count = random.randint(0, 10)
                template.save()

        self.stdout.write('Created ticket templates')

    def create_sample_tickets(self):
        """Create sample tickets"""
        clients = list(Client.objects.all()[:5])  # Get first 5 clients
        projects = list(Project.objects.all()[:3])  # Get first 3 projects
        admin_user = User.objects.filter(is_staff=True).first()
        
        if not clients:
            self.stdout.write(self.style.WARNING('No clients found. Please create clients first.'))
            return
        
        ticket_data = [
            {
                'title': 'مشكلة في تسجيل الدخول',
                'description': 'لا أستطيع تسجيل الدخول إلى النظام. تظهر رسالة خطأ عند إدخال كلمة المرور.',
                'priority': 'high',
                'category': 'technical',
                'source': 'email',
                'status': 'open',
            },
            {
                'title': 'طلب تحديث معلومات المشروع',
                'description': 'أريد تحديث معلومات المشروع الخاص بي وإضافة متطلبات جديدة.',
                'priority': 'medium',
                'category': 'project_related',
                'source': 'web_portal',
                'status': 'in_progress',
            },
            {
                'title': 'استفسار عن الفاتورة',
                'description': 'لدي استفسار حول الفاتورة الأخيرة. هناك رسوم غير واضحة.',
                'priority': 'medium',
                'category': 'billing',
                'source': 'phone',
                'status': 'pending_client',
            },
            {
                'title': 'طلب ميزة جديدة',
                'description': 'أقترح إضافة ميزة التقارير المخصصة للنظام.',
                'priority': 'low',
                'category': 'feature_request',
                'source': 'web_portal',
                'status': 'resolved',
            },
            {
                'title': 'خطأ في النظام',
                'description': 'يظهر خطأ 500 عند محاولة حفظ البيانات في صفحة المشاريع.',
                'priority': 'critical',
                'category': 'bug_report',
                'source': 'email',
                'status': 'escalated',
            },
        ]

        for i, data in enumerate(ticket_data):
            client = random.choice(clients)
            project = random.choice(projects) if projects and random.choice([True, False]) else None
            
            # Create ticket
            ticket = Ticket.objects.create(
                title=data['title'],
                description=data['description'],
                client=client,
                project=project,
                priority=data['priority'],
                category=data['category'],
                source=data['source'],
                status=data['status'],
                created_by=admin_user,
                assigned_to=admin_user if data['status'] != 'open' else None,
                created_at=timezone.now() - timedelta(days=random.randint(1, 30))
            )
            
            # Add some responses
            if data['status'] != 'open':
                TicketResponse.objects.create(
                    ticket=ticket,
                    user=admin_user,
                    response_text=f'شكراً لتواصلك معنا. تم استلام طلبك وسيتم الرد عليك قريباً.',
                    response_type='comment',
                    is_internal=False,
                    created_at=ticket.created_at + timedelta(hours=2)
                )
            
            if data['status'] in ['resolved', 'closed']:
                TicketResponse.objects.create(
                    ticket=ticket,
                    user=admin_user,
                    response_text=f'تم حل المشكلة بنجاح. يرجى التحقق والتأكيد.',
                    response_type='resolution',
                    is_internal=False,
                    created_at=ticket.created_at + timedelta(days=1)
                )
                ticket.resolved_at = ticket.created_at + timedelta(days=1)
                ticket.satisfaction_rating = random.randint(4, 5)
                ticket.satisfaction_feedback = 'خدمة ممتازة وحل سريع للمشكلة'
                ticket.save()

        self.stdout.write('Created sample tickets')
