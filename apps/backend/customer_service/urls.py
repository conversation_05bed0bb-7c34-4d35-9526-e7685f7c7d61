"""
Customer Service Portal URLs
Following DRY, ULM, CPR, GRS, VST, SMA, CBR, BOP, API-First Mentality principles
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import (
    SLARuleViewSet,
    KnowledgeBaseViewSet,
    TicketViewSet,
    TicketResponseViewSet,
    TicketTemplateViewSet
)

# Create router and register viewsets
router = DefaultRouter()
router.register(r'sla-rules', SLARuleViewSet, basename='slarule')
router.register(r'knowledge-base', KnowledgeBaseViewSet, basename='knowledgebase')
router.register(r'tickets', TicketViewSet, basename='ticket')
router.register(r'ticket-responses', TicketResponseViewSet, basename='ticketresponse')
router.register(r'ticket-templates', TicketTemplateViewSet, basename='tickettemplate')

urlpatterns = [
    path('', include(router.urls)),
]
