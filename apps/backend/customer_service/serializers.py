"""
Customer Service Portal Serializers
Following DRY, ULM, CPR, GRS, VST, SMA, CBR, BOP, API-First Mentality principles
"""

from rest_framework import serializers
from django.contrib.auth import get_user_model
from clients.models import Client
from projects.models import Project
from .models import (
    SLARule,
    KnowledgeBase,
    Ticket,
    TicketResponse,
    TicketEscalation,
    TicketTemplate,
)

User = get_user_model()


# Shared serializers for DRY principle
class UserListSerializer(serializers.ModelSerializer):
    """Lightweight user serializer for lists"""

    full_name = serializers.CharField(read_only=True)

    class Meta:
        model = User
        fields = ["id", "username", "first_name", "last_name", "full_name", "role"]


class ClientListSerializer(serializers.ModelSerializer):
    """Lightweight client serializer for lists"""

    class Meta:
        model = Client
        fields = ["id", "name", "email", "company", "phone"]


class ProjectListSerializer(serializers.ModelSerializer):
    """Lightweight project serializer for lists"""

    class Meta:
        model = Project
        fields = ["id", "name", "type", "status"]


# SLA Rule Serializers
class SLARuleSerializer(serializers.ModelSerializer):
    """Complete SLA rule serializer"""

    priority_display = serializers.CharField(
        source="get_priority_display", read_only=True
    )
    client_tier_display = serializers.CharField(
        source="get_client_tier_display", read_only=True
    )

    class Meta:
        model = SLARule
        fields = [
            "id",
            "name",
            "priority",
            "priority_display",
            "client_tier",
            "client_tier_display",
            "response_time_hours",
            "resolution_time_hours",
            "escalation_time_hours",
            "business_hours_only",
            "is_active",
            "created_at",
            "updated_at",
        ]


class SLARuleCreateSerializer(serializers.ModelSerializer):
    """SLA rule creation serializer"""

    class Meta:
        model = SLARule
        fields = [
            "name",
            "priority",
            "client_tier",
            "response_time_hours",
            "resolution_time_hours",
            "escalation_time_hours",
            "business_hours_only",
        ]


# Knowledge Base Serializers
class KnowledgeBaseSerializer(serializers.ModelSerializer):
    """Complete knowledge base serializer"""

    created_by = UserListSerializer(read_only=True)
    category_display = serializers.CharField(
        source="get_category_display", read_only=True
    )
    helpfulness_ratio = serializers.ReadOnlyField()

    class Meta:
        model = KnowledgeBase
        fields = [
            "id",
            "title_ar",
            "title_en",
            "content_ar",
            "content_en",
            "category",
            "category_display",
            "tags",
            "is_public",
            "is_featured",
            "view_count",
            "helpful_count",
            "not_helpful_count",
            "helpfulness_ratio",
            "created_by",
            "created_at",
            "updated_at",
        ]


class KnowledgeBaseListSerializer(serializers.ModelSerializer):
    """Lightweight knowledge base serializer for lists"""

    category_display = serializers.CharField(
        source="get_category_display", read_only=True
    )
    helpfulness_ratio = serializers.ReadOnlyField()

    class Meta:
        model = KnowledgeBase
        fields = [
            "id",
            "title_ar",
            "title_en",
            "category",
            "category_display",
            "tags",
            "is_featured",
            "view_count",
            "helpfulness_ratio",
            "created_at",
        ]


class KnowledgeBaseCreateSerializer(serializers.ModelSerializer):
    """Knowledge base creation serializer"""

    class Meta:
        model = KnowledgeBase
        fields = [
            "title_ar",
            "title_en",
            "content_ar",
            "content_en",
            "category",
            "tags",
            "is_public",
            "is_featured",
        ]

    def create(self, validated_data):
        """Create knowledge base article"""
        validated_data["created_by"] = self.context["request"].user
        return super().create(validated_data)


# Ticket Response Serializers
class TicketResponseSerializer(serializers.ModelSerializer):
    """Complete ticket response serializer"""

    user = UserListSerializer(read_only=True)
    response_type_display = serializers.CharField(
        source="get_response_type_display", read_only=True
    )

    class Meta:
        model = TicketResponse
        fields = [
            "id",
            "response_text",
            "response_type",
            "response_type_display",
            "is_internal",
            "attachments",
            "user",
            "created_at",
            "updated_at",
        ]


class TicketResponseCreateSerializer(serializers.ModelSerializer):
    """Ticket response creation serializer"""

    class Meta:
        model = TicketResponse
        fields = ["response_text", "response_type", "is_internal", "attachments"]

    def create(self, validated_data):
        """Create ticket response"""
        validated_data["user"] = self.context["request"].user
        validated_data["ticket"] = self.context["ticket"]
        return super().create(validated_data)


# Ticket Escalation Serializers
class TicketEscalationSerializer(serializers.ModelSerializer):
    """Complete ticket escalation serializer"""

    escalated_from = UserListSerializer(read_only=True)
    escalated_to = UserListSerializer(read_only=True)
    escalated_by = UserListSerializer(read_only=True)
    reason_display = serializers.CharField(source="get_reason_display", read_only=True)

    class Meta:
        model = TicketEscalation
        fields = [
            "id",
            "reason",
            "reason_display",
            "notes",
            "escalation_level",
            "escalated_from",
            "escalated_to",
            "escalated_by",
            "created_at",
        ]


# Ticket Serializers
class TicketSerializer(serializers.ModelSerializer):
    """Complete ticket serializer"""

    client = ClientListSerializer(read_only=True)
    project = ProjectListSerializer(read_only=True)
    assigned_to = UserListSerializer(read_only=True)
    created_by = UserListSerializer(read_only=True)

    # Display fields
    status_display = serializers.CharField(source="get_status_display", read_only=True)
    priority_display = serializers.CharField(
        source="get_priority_display", read_only=True
    )
    category_display = serializers.CharField(
        source="get_category_display", read_only=True
    )
    source_display = serializers.CharField(source="get_source_display", read_only=True)

    # Computed fields
    is_overdue = serializers.BooleanField(read_only=True)
    time_to_resolution = serializers.DurationField(read_only=True)
    response_time = serializers.DurationField(read_only=True)

    # Related data
    responses = TicketResponseSerializer(many=True, read_only=True)
    escalations = TicketEscalationSerializer(many=True, read_only=True)
    responses_count = serializers.SerializerMethodField()

    class Meta:
        model = Ticket
        fields = [
            "id",
            "ticket_number",
            "title",
            "description",
            "client",
            "project",
            "assigned_to",
            "created_by",
            "status",
            "status_display",
            "priority",
            "priority_display",
            "category",
            "category_display",
            "source",
            "source_display",
            "sla_due_date",
            "escalation_level",
            "created_at",
            "updated_at",
            "resolved_at",
            "closed_at",
            "satisfaction_rating",
            "satisfaction_feedback",
            "attachments",
            "metadata",
            "is_overdue",
            "time_to_resolution",
            "response_time",
            "responses",
            "escalations",
            "responses_count",
        ]
        read_only_fields = [
            "id",
            "ticket_number",
            "sla_due_date",
            "escalation_level",
            "created_at",
            "updated_at",
            "resolved_at",
            "closed_at",
        ]

    def get_responses_count(self, obj):
        """Get number of responses"""
        return obj.responses.count()


class TicketListSerializer(serializers.ModelSerializer):
    """Lightweight ticket serializer for lists"""

    client = ClientListSerializer(read_only=True)
    project = ProjectListSerializer(read_only=True)
    assigned_to = UserListSerializer(read_only=True)

    # Display fields
    status_display = serializers.CharField(source="get_status_display", read_only=True)
    priority_display = serializers.CharField(
        source="get_priority_display", read_only=True
    )
    category_display = serializers.CharField(
        source="get_category_display", read_only=True
    )

    # Computed fields
    is_overdue = serializers.BooleanField(read_only=True)
    responses_count = serializers.SerializerMethodField()

    class Meta:
        model = Ticket
        fields = [
            "id",
            "ticket_number",
            "title",
            "client",
            "project",
            "assigned_to",
            "status",
            "status_display",
            "priority",
            "priority_display",
            "category",
            "category_display",
            "sla_due_date",
            "created_at",
            "is_overdue",
            "responses_count",
        ]

    def get_responses_count(self, obj):
        """Get number of responses"""
        return obj.responses.count()


class TicketCreateSerializer(serializers.ModelSerializer):
    """Ticket creation serializer"""

    client_id = serializers.IntegerField(write_only=True)
    project_id = serializers.IntegerField(
        write_only=True, required=False, allow_null=True
    )

    class Meta:
        model = Ticket
        fields = [
            "title",
            "description",
            "client_id",
            "project_id",
            "priority",
            "category",
            "source",
            "attachments",
            "metadata",
        ]

    def validate_client_id(self, value):
        """Validate client exists"""
        try:
            Client.objects.get(id=value)
            return value
        except Client.DoesNotExist:
            raise serializers.ValidationError("العميل المحدد غير موجود")

    def validate_project_id(self, value):
        """Validate project exists if provided"""
        if value is not None:
            try:
                Project.objects.get(id=value)
                return value
            except Project.DoesNotExist:
                raise serializers.ValidationError("المشروع المحدد غير موجود")
        return value

    def create(self, validated_data):
        """Create ticket"""
        client_id = validated_data.pop("client_id")
        project_id = validated_data.pop("project_id", None)

        validated_data["client_id"] = client_id
        if project_id:
            validated_data["project_id"] = project_id
        validated_data["created_by"] = self.context["request"].user

        return super().create(validated_data)


class TicketUpdateSerializer(serializers.ModelSerializer):
    """Ticket update serializer"""

    assigned_to_id = serializers.IntegerField(
        write_only=True, required=False, allow_null=True
    )

    class Meta:
        model = Ticket
        fields = [
            "title",
            "description",
            "status",
            "priority",
            "category",
            "assigned_to_id",
            "satisfaction_rating",
            "satisfaction_feedback",
            "attachments",
            "metadata",
        ]

    def validate_assigned_to_id(self, value):
        """Validate assigned user exists if provided"""
        if value is not None:
            try:
                User.objects.get(id=value)
                return value
            except User.DoesNotExist:
                raise serializers.ValidationError("المستخدم المحدد غير موجود")
        return value

    def update(self, instance, validated_data):
        """Update ticket with status change tracking"""
        assigned_to_id = validated_data.pop("assigned_to_id", None)
        if assigned_to_id is not None:
            validated_data["assigned_to_id"] = assigned_to_id

        # Track status changes
        old_status = instance.status
        new_status = validated_data.get("status", old_status)

        # Update resolved_at when status changes to resolved
        if (
            new_status == Ticket.Status.RESOLVED
            and old_status != Ticket.Status.RESOLVED
        ):
            from django.utils import timezone

            validated_data["resolved_at"] = timezone.now()

        # Update closed_at when status changes to closed
        if new_status == Ticket.Status.CLOSED and old_status != Ticket.Status.CLOSED:
            from django.utils import timezone

            validated_data["closed_at"] = timezone.now()

        return super().update(instance, validated_data)


# Ticket Template Serializers
class TicketTemplateSerializer(serializers.ModelSerializer):
    """Complete ticket template serializer"""

    created_by = UserListSerializer(read_only=True)
    template_type_display = serializers.CharField(
        source="get_template_type_display", read_only=True
    )
    category_display = serializers.CharField(
        source="get_category_display", read_only=True
    )

    class Meta:
        model = TicketTemplate
        fields = [
            "id",
            "name",
            "description",
            "template_type",
            "template_type_display",
            "subject_template",
            "body_template",
            "category",
            "category_display",
            "is_active",
            "usage_count",
            "created_by",
            "created_at",
            "updated_at",
        ]


class TicketTemplateListSerializer(serializers.ModelSerializer):
    """Lightweight ticket template serializer for lists"""

    template_type_display = serializers.CharField(
        source="get_template_type_display", read_only=True
    )
    category_display = serializers.CharField(
        source="get_category_display", read_only=True
    )

    class Meta:
        model = TicketTemplate
        fields = [
            "id",
            "name",
            "template_type",
            "template_type_display",
            "category",
            "category_display",
            "is_active",
            "usage_count",
        ]


class TicketTemplateCreateSerializer(serializers.ModelSerializer):
    """Ticket template creation serializer"""

    class Meta:
        model = TicketTemplate
        fields = [
            "name",
            "description",
            "template_type",
            "subject_template",
            "body_template",
            "category",
            "is_active",
        ]

    def create(self, validated_data):
        """Create ticket template"""
        validated_data["created_by"] = self.context["request"].user
        return super().create(validated_data)


# Statistics and Analytics Serializers
class TicketStatsSerializer(serializers.Serializer):
    """Ticket statistics serializer"""

    total_tickets = serializers.IntegerField()
    open_tickets = serializers.IntegerField()
    in_progress_tickets = serializers.IntegerField()
    resolved_tickets = serializers.IntegerField()
    closed_tickets = serializers.IntegerField()
    overdue_tickets = serializers.IntegerField()

    # Priority breakdown
    critical_tickets = serializers.IntegerField()
    high_tickets = serializers.IntegerField()
    medium_tickets = serializers.IntegerField()
    low_tickets = serializers.IntegerField()

    # Performance metrics
    avg_response_time_hours = serializers.FloatField()
    avg_resolution_time_hours = serializers.FloatField()
    satisfaction_rating = serializers.FloatField()

    # Trends
    tickets_this_week = serializers.IntegerField()
    tickets_last_week = serializers.IntegerField()
    resolution_rate = serializers.FloatField()


class CustomerServiceDashboardSerializer(serializers.Serializer):
    """Customer service dashboard data serializer"""

    stats = TicketStatsSerializer()
    recent_tickets = TicketListSerializer(many=True)
    overdue_tickets = TicketListSerializer(many=True)
    my_tickets = TicketListSerializer(many=True)
    popular_kb_articles = KnowledgeBaseListSerializer(many=True)

    # Charts data
    tickets_by_status = serializers.DictField()
    tickets_by_priority = serializers.DictField()
    tickets_by_category = serializers.DictField()
    resolution_trend = serializers.ListField()


# Escalation Serializers
class TicketEscalationCreateSerializer(serializers.ModelSerializer):
    """Ticket escalation creation serializer"""

    escalated_to_id = serializers.IntegerField(write_only=True)

    class Meta:
        model = TicketEscalation
        fields = ["reason", "notes", "escalated_to_id"]

    def validate_escalated_to_id(self, value):
        """Validate escalated to user exists"""
        try:
            User.objects.get(id=value)
            return value
        except User.DoesNotExist:
            raise serializers.ValidationError("المستخدم المحدد غير موجود")

    def create(self, validated_data):
        """Create ticket escalation"""
        escalated_to_id = validated_data.pop("escalated_to_id")
        ticket = self.context["ticket"]

        validated_data["ticket"] = ticket
        validated_data["escalated_to_id"] = escalated_to_id
        validated_data["escalated_from"] = ticket.assigned_to
        validated_data["escalated_by"] = self.context["request"].user
        validated_data["escalation_level"] = ticket.escalation_level + 1

        # Update ticket
        ticket.assigned_to_id = escalated_to_id
        ticket.escalation_level += 1
        ticket.status = Ticket.Status.ESCALATED
        ticket.save()

        return super().create(validated_data)
