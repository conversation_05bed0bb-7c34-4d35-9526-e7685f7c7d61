"""
Customer Service Portal Signals
Following DRY, ULM, CPR, GRS, VST, SMA, CBR, BOP, API-First Mentality principles
"""

from django.db.models.signals import post_save, pre_save
from django.dispatch import receiver
from django.utils import timezone
from datetime import <PERSON><PERSON><PERSON>
from .models import Ticket, TicketResponse, TicketEscalation


@receiver(pre_save, sender=Ticket)
def ticket_pre_save(sender, instance, **kwargs):
    """Handle ticket pre-save operations"""
    # Generate ticket number if not set
    if not instance.ticket_number:
        instance.ticket_number = instance.generate_ticket_number()
    
    # Set SLA due date if not set and status is open
    if not instance.sla_due_date and instance.status == Ticket.Status.OPEN:
        instance.set_sla_due_date()


@receiver(post_save, sender=Ticket)
def ticket_post_save(sender, instance, created, **kwargs):
    """Handle ticket post-save operations"""
    if created:
        # Create initial response for ticket creation
        TicketResponse.objects.create(
            ticket=instance,
            user=instance.created_by,
            response_text=f'تم إنشاء التذكرة {instance.ticket_number}',
            response_type=TicketResponse.ResponseType.COMMENT,
            is_internal=True
        )
        
        # TODO: Send notification to assigned user if assigned
        # TODO: Send email notification to client
        
    else:
        # Check if status changed
        if hasattr(instance, '_original_status'):
            old_status = instance._original_status
            new_status = instance.status
            
            if old_status != new_status:
                # Create status change response
                status_messages = {
                    Ticket.Status.OPEN: 'تم فتح التذكرة',
                    Ticket.Status.IN_PROGRESS: 'التذكرة قيد المعالجة',
                    Ticket.Status.PENDING_CLIENT: 'في انتظار رد العميل',
                    Ticket.Status.RESOLVED: 'تم حل التذكرة',
                    Ticket.Status.CLOSED: 'تم إغلاق التذكرة',
                    Ticket.Status.ESCALATED: 'تم تصعيد التذكرة',
                }
                
                TicketResponse.objects.create(
                    ticket=instance,
                    user=None,  # System generated
                    response_text=status_messages.get(new_status, f'تم تغيير الحالة إلى {new_status}'),
                    response_type=TicketResponse.ResponseType.STATUS_CHANGE,
                    is_internal=True
                )
                
                # TODO: Send status change notifications


@receiver(post_save, sender=TicketResponse)
def ticket_response_post_save(sender, instance, created, **kwargs):
    """Handle ticket response post-save operations"""
    if created:
        # Update ticket's updated_at timestamp
        instance.ticket.save(update_fields=['updated_at'])
        
        # TODO: Send notification to relevant users
        # TODO: Send email notification if not internal


@receiver(post_save, sender=TicketEscalation)
def ticket_escalation_post_save(sender, instance, created, **kwargs):
    """Handle ticket escalation post-save operations"""
    if created:
        # Create escalation response
        TicketResponse.objects.create(
            ticket=instance.ticket,
            user=instance.escalated_by,
            response_text=f'تم تصعيد التذكرة إلى {instance.escalated_to.get_full_name()} - السبب: {instance.get_reason_display()}',
            response_type=TicketResponse.ResponseType.ESCALATION,
            is_internal=True
        )
        
        # TODO: Send escalation notifications


# Store original status for comparison
@receiver(pre_save, sender=Ticket)
def store_original_status(sender, instance, **kwargs):
    """Store original status for comparison"""
    if instance.pk:
        try:
            original = Ticket.objects.get(pk=instance.pk)
            instance._original_status = original.status
        except Ticket.DoesNotExist:
            instance._original_status = None
    else:
        instance._original_status = None
