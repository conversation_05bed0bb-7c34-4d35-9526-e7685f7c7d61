"""
Customer Service Portal Views
Following DRY, ULM, CPR, GRS, VST, SMA, CBR, BOP, API-First Mentality principles
"""

from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Count, Sum, Avg, Q, F
from django.utils import timezone
from datetime import datetime, timedelta

from .models import (
    SLARule,
    KnowledgeBase,
    Ticket,
    TicketResponse,
    TicketEscalation,
    TicketTemplate,
)
from .serializers import (
    SLARuleSerializer,
    SLARuleCreateSerializer,
    KnowledgeBaseSerializer,
    KnowledgeBaseListSerializer,
    KnowledgeBaseCreateSerializer,
    TicketSerializer,
    TicketListSerializer,
    TicketCreateSerializer,
    TicketUpdateSerializer,
    TicketResponseSerializer,
    TicketResponseCreateSerializer,
    TicketEscalationSerializer,
    TicketEscalationCreateSerializer,
    TicketTemplateSerializer,
    TicketTemplateListSerializer,
    TicketTemplateCreateSerializer,
    TicketStatsSerializer,
    CustomerServiceDashboardSerializer,
)


class SLARuleViewSet(viewsets.ModelViewSet):
    """SLA Rule management viewset"""

    queryset = SLARule.objects.all().order_by("priority", "client_tier")
    permission_classes = [IsAuthenticated]
    filter_backends = [
        DjangoFilterBackend,
        filters.SearchFilter,
        filters.OrderingFilter,
    ]
    filterset_fields = ["priority", "client_tier", "is_active"]
    search_fields = ["name"]
    ordering_fields = ["priority", "client_tier", "response_time_hours"]
    ordering = ["priority", "client_tier"]

    def get_serializer_class(self):
        """Return appropriate serializer based on action"""
        if self.action == "create":
            return SLARuleCreateSerializer
        elif self.action == "list":
            return SLARuleSerializer
        return SLARuleSerializer


class KnowledgeBaseViewSet(viewsets.ModelViewSet):
    """Knowledge Base management viewset"""

    queryset = KnowledgeBase.objects.all().order_by("-is_featured", "-created_at")
    permission_classes = [IsAuthenticated]
    filter_backends = [
        DjangoFilterBackend,
        filters.SearchFilter,
        filters.OrderingFilter,
    ]
    filterset_fields = ["category", "is_public", "is_featured"]
    search_fields = ["title_ar", "title_en", "content_ar", "content_en", "tags"]
    ordering_fields = ["created_at", "view_count", "helpful_count"]
    ordering = ["-is_featured", "-created_at"]

    def get_serializer_class(self):
        """Return appropriate serializer based on action"""
        if self.action == "create":
            return KnowledgeBaseCreateSerializer
        elif self.action == "list":
            return KnowledgeBaseListSerializer
        return KnowledgeBaseSerializer

    def get_queryset(self):
        """Filter queryset based on user permissions"""
        queryset = super().get_queryset()

        # Non-admin users can only see public articles
        if not self.request.user.is_staff:
            queryset = queryset.filter(is_public=True)

        return queryset

    @action(detail=True, methods=["post"])
    def mark_helpful(self, request, pk=None):
        """Mark article as helpful"""
        article = self.get_object()
        article.helpful_count = F("helpful_count") + 1
        article.save(update_fields=["helpful_count"])
        return Response({"status": "marked as helpful"})

    @action(detail=True, methods=["post"])
    def mark_not_helpful(self, request, pk=None):
        """Mark article as not helpful"""
        article = self.get_object()
        article.not_helpful_count = F("not_helpful_count") + 1
        article.save(update_fields=["not_helpful_count"])
        return Response({"status": "marked as not helpful"})

    @action(detail=True, methods=["post"])
    def increment_view(self, request, pk=None):
        """Increment view count"""
        article = self.get_object()
        article.view_count = F("view_count") + 1
        article.save(update_fields=["view_count"])
        return Response({"status": "view count incremented"})

    @action(detail=False, methods=["get"])
    def popular(self, request):
        """Get popular articles"""
        articles = self.get_queryset().order_by("-view_count")[:10]
        serializer = KnowledgeBaseListSerializer(articles, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=["get"])
    def featured(self, request):
        """Get featured articles"""
        articles = self.get_queryset().filter(is_featured=True)
        serializer = KnowledgeBaseListSerializer(articles, many=True)
        return Response(serializer.data)


class TicketViewSet(viewsets.ModelViewSet):
    """Ticket management viewset"""

    queryset = (
        Ticket.objects.all()
        .select_related("client", "project", "assigned_to", "created_by")
        .prefetch_related("responses", "escalations")
        .order_by("-created_at")
    )
    permission_classes = [IsAuthenticated]
    filter_backends = [
        DjangoFilterBackend,
        filters.SearchFilter,
        filters.OrderingFilter,
    ]
    filterset_fields = [
        "status",
        "priority",
        "category",
        "source",
        "client",
        "assigned_to",
    ]
    search_fields = [
        "ticket_number",
        "title",
        "description",
        "client__name",
        "client__email",
    ]
    ordering_fields = ["ticket_number", "created_at", "sla_due_date", "priority"]
    ordering = ["-created_at"]

    def get_serializer_class(self):
        """Return appropriate serializer based on action"""
        if self.action == "create":
            return TicketCreateSerializer
        elif self.action in ["update", "partial_update"]:
            return TicketUpdateSerializer
        elif self.action == "list":
            return TicketListSerializer
        return TicketSerializer

    def get_queryset(self):
        """Filter queryset based on user permissions and query parameters"""
        queryset = super().get_queryset()

        # Filter by assignment if requested
        if self.request.query_params.get("my_tickets"):
            queryset = queryset.filter(assigned_to=self.request.user)

        # Filter by overdue status
        if self.request.query_params.get("overdue"):
            now = timezone.now()
            queryset = queryset.filter(
                sla_due_date__lt=now,
                status__in=[Ticket.Status.OPEN, Ticket.Status.IN_PROGRESS],
            )

        # Filter by date range
        date_from = self.request.query_params.get("date_from")
        date_to = self.request.query_params.get("date_to")
        if date_from:
            queryset = queryset.filter(created_at__gte=date_from)
        if date_to:
            queryset = queryset.filter(created_at__lte=date_to)

        return queryset

    @action(detail=True, methods=["post"])
    def assign(self, request, pk=None):
        """Assign ticket to user"""
        ticket = self.get_object()
        user_id = request.data.get("user_id")

        if not user_id:
            return Response(
                {"error": "user_id is required"}, status=status.HTTP_400_BAD_REQUEST
            )

        try:
            from django.contrib.auth import get_user_model

            User = get_user_model()
            user = User.objects.get(id=user_id)
            ticket.assigned_to = user
            ticket.save()

            # Create response for assignment
            TicketResponse.objects.create(
                ticket=ticket,
                user=request.user,
                response_text=f"تم تكليف التذكرة إلى {user.get_full_name()}",
                response_type=TicketResponse.ResponseType.ASSIGNMENT,
                is_internal=True,
            )

            serializer = self.get_serializer(ticket)
            return Response(serializer.data)

        except User.DoesNotExist:
            return Response(
                {"error": "User not found"}, status=status.HTTP_404_NOT_FOUND
            )

    @action(detail=True, methods=["post"])
    def escalate(self, request, pk=None):
        """Escalate ticket"""
        ticket = self.get_object()
        serializer = TicketEscalationCreateSerializer(
            data=request.data, context={"request": request, "ticket": ticket}
        )

        if serializer.is_valid():
            escalation = serializer.save()
            return Response(
                TicketEscalationSerializer(escalation).data,
                status=status.HTTP_201_CREATED,
            )
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=["post"])
    def add_response(self, request, pk=None):
        """Add response to ticket"""
        ticket = self.get_object()
        serializer = TicketResponseCreateSerializer(
            data=request.data, context={"request": request, "ticket": ticket}
        )

        if serializer.is_valid():
            response = serializer.save()
            return Response(
                TicketResponseSerializer(response).data, status=status.HTTP_201_CREATED
            )
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=["post"])
    def resolve(self, request, pk=None):
        """Mark ticket as resolved"""
        ticket = self.get_object()
        resolution_notes = request.data.get("resolution_notes", "")

        ticket.status = Ticket.Status.RESOLVED
        ticket.resolved_at = timezone.now()
        ticket.save()

        # Create resolution response
        TicketResponse.objects.create(
            ticket=ticket,
            user=request.user,
            response_text=resolution_notes or "تم حل التذكرة",
            response_type=TicketResponse.ResponseType.RESOLUTION,
            is_internal=False,
        )

        serializer = self.get_serializer(ticket)
        return Response(serializer.data)

    @action(detail=True, methods=["post"])
    def close(self, request, pk=None):
        """Close ticket"""
        ticket = self.get_object()

        ticket.status = Ticket.Status.CLOSED
        ticket.closed_at = timezone.now()
        ticket.save()

        serializer = self.get_serializer(ticket)
        return Response(serializer.data)

    @action(detail=False, methods=["get"])
    def stats(self, request):
        """Get ticket statistics"""
        queryset = self.get_queryset()

        # Basic counts
        total_tickets = queryset.count()
        open_tickets = queryset.filter(status=Ticket.Status.OPEN).count()
        in_progress_tickets = queryset.filter(status=Ticket.Status.IN_PROGRESS).count()
        resolved_tickets = queryset.filter(status=Ticket.Status.RESOLVED).count()
        closed_tickets = queryset.filter(status=Ticket.Status.CLOSED).count()

        # Overdue tickets
        now = timezone.now()
        overdue_tickets = queryset.filter(
            sla_due_date__lt=now,
            status__in=[Ticket.Status.OPEN, Ticket.Status.IN_PROGRESS],
        ).count()

        # Priority breakdown
        critical_tickets = queryset.filter(priority=Ticket.Priority.CRITICAL).count()
        high_tickets = queryset.filter(priority=Ticket.Priority.HIGH).count()
        medium_tickets = queryset.filter(priority=Ticket.Priority.MEDIUM).count()
        low_tickets = queryset.filter(priority=Ticket.Priority.LOW).count()

        # Performance metrics
        resolved_tickets_with_times = queryset.filter(
            status=Ticket.Status.RESOLVED, resolved_at__isnull=False
        )

        avg_resolution_time = None
        if resolved_tickets_with_times.exists():
            resolution_times = [
                (ticket.resolved_at - ticket.created_at).total_seconds() / 3600
                for ticket in resolved_tickets_with_times
            ]
            avg_resolution_time = sum(resolution_times) / len(resolution_times)

        # Satisfaction rating
        satisfaction_ratings = queryset.filter(
            satisfaction_rating__isnull=False
        ).aggregate(avg_rating=Avg("satisfaction_rating"))

        # Weekly trends
        week_ago = now - timedelta(days=7)
        two_weeks_ago = now - timedelta(days=14)

        tickets_this_week = queryset.filter(created_at__gte=week_ago).count()
        tickets_last_week = queryset.filter(
            created_at__gte=two_weeks_ago, created_at__lt=week_ago
        ).count()

        resolution_rate = 0
        if total_tickets > 0:
            resolution_rate = (resolved_tickets + closed_tickets) / total_tickets * 100

        stats_data = {
            "total_tickets": total_tickets,
            "open_tickets": open_tickets,
            "in_progress_tickets": in_progress_tickets,
            "resolved_tickets": resolved_tickets,
            "closed_tickets": closed_tickets,
            "overdue_tickets": overdue_tickets,
            "critical_tickets": critical_tickets,
            "high_tickets": high_tickets,
            "medium_tickets": medium_tickets,
            "low_tickets": low_tickets,
            "avg_response_time_hours": 0,  # TODO: Calculate from first response
            "avg_resolution_time_hours": avg_resolution_time or 0,
            "satisfaction_rating": satisfaction_ratings["avg_rating"] or 0,
            "tickets_this_week": tickets_this_week,
            "tickets_last_week": tickets_last_week,
            "resolution_rate": resolution_rate,
        }

        serializer = TicketStatsSerializer(stats_data)
        return Response(serializer.data)

    @action(detail=False, methods=["get"])
    def dashboard(self, request):
        """Get customer service dashboard data"""
        queryset = self.get_queryset()

        # Get stats
        stats_response = self.stats(request)
        stats_data = stats_response.data

        # Recent tickets
        recent_tickets = queryset[:10]
        recent_tickets_data = TicketListSerializer(recent_tickets, many=True).data

        # Overdue tickets
        now = timezone.now()
        overdue_tickets = queryset.filter(
            sla_due_date__lt=now,
            status__in=[Ticket.Status.OPEN, Ticket.Status.IN_PROGRESS],
        )[:10]
        overdue_tickets_data = TicketListSerializer(overdue_tickets, many=True).data

        # My tickets (assigned to current user)
        my_tickets = queryset.filter(assigned_to=request.user)[:10]
        my_tickets_data = TicketListSerializer(my_tickets, many=True).data

        # Popular KB articles
        popular_kb = KnowledgeBase.objects.filter(is_public=True).order_by(
            "-view_count"
        )[:5]
        popular_kb_data = KnowledgeBaseListSerializer(popular_kb, many=True).data

        # Charts data
        tickets_by_status = {
            "open": queryset.filter(status=Ticket.Status.OPEN).count(),
            "in_progress": queryset.filter(status=Ticket.Status.IN_PROGRESS).count(),
            "resolved": queryset.filter(status=Ticket.Status.RESOLVED).count(),
            "closed": queryset.filter(status=Ticket.Status.CLOSED).count(),
        }

        tickets_by_priority = {
            "critical": queryset.filter(priority=Ticket.Priority.CRITICAL).count(),
            "high": queryset.filter(priority=Ticket.Priority.HIGH).count(),
            "medium": queryset.filter(priority=Ticket.Priority.MEDIUM).count(),
            "low": queryset.filter(priority=Ticket.Priority.LOW).count(),
        }

        tickets_by_category = {}
        for category in Ticket.Category.choices:
            tickets_by_category[category[0]] = queryset.filter(
                category=category[0]
            ).count()

        # Resolution trend (last 7 days)
        resolution_trend = []
        for i in range(7):
            date = timezone.now().date() - timedelta(days=i)
            resolved_count = queryset.filter(resolved_at__date=date).count()
            resolution_trend.append(
                {"date": date.isoformat(), "resolved": resolved_count}
            )

        dashboard_data = {
            "stats": stats_data,
            "recent_tickets": recent_tickets_data,
            "overdue_tickets": overdue_tickets_data,
            "my_tickets": my_tickets_data,
            "popular_kb_articles": popular_kb_data,
            "tickets_by_status": tickets_by_status,
            "tickets_by_priority": tickets_by_priority,
            "tickets_by_category": tickets_by_category,
            "resolution_trend": resolution_trend,
        }

        serializer = CustomerServiceDashboardSerializer(dashboard_data)
        return Response(serializer.data)


class TicketResponseViewSet(viewsets.ModelViewSet):
    """Ticket Response management viewset"""

    queryset = (
        TicketResponse.objects.all()
        .select_related("ticket", "user")
        .order_by("created_at")
    )
    permission_classes = [IsAuthenticated]
    filter_backends = [
        DjangoFilterBackend,
        filters.SearchFilter,
        filters.OrderingFilter,
    ]
    filterset_fields = ["ticket", "user", "response_type", "is_internal"]
    search_fields = ["response_text"]
    ordering_fields = ["created_at"]
    ordering = ["created_at"]

    def get_serializer_class(self):
        """Return appropriate serializer based on action"""
        if self.action == "create":
            return TicketResponseCreateSerializer
        return TicketResponseSerializer

    def get_queryset(self):
        """Filter responses by ticket if provided"""
        queryset = super().get_queryset()
        ticket_id = self.request.query_params.get("ticket_id")
        if ticket_id:
            queryset = queryset.filter(ticket_id=ticket_id)
        return queryset


class TicketTemplateViewSet(viewsets.ModelViewSet):
    """Ticket Template management viewset"""

    queryset = (
        TicketTemplate.objects.all()
        .select_related("created_by")
        .order_by("template_type", "name")
    )
    permission_classes = [IsAuthenticated]
    filter_backends = [
        DjangoFilterBackend,
        filters.SearchFilter,
        filters.OrderingFilter,
    ]
    filterset_fields = ["template_type", "category", "is_active"]
    search_fields = ["name", "description", "body_template"]
    ordering_fields = ["name", "template_type", "usage_count"]
    ordering = ["template_type", "name"]

    def get_serializer_class(self):
        """Return appropriate serializer based on action"""
        if self.action == "create":
            return TicketTemplateCreateSerializer
        elif self.action == "list":
            return TicketTemplateListSerializer
        return TicketTemplateSerializer

    @action(detail=True, methods=["post"])
    def use_template(self, request, pk=None):
        """Use template and increment usage count"""
        template = self.get_object()
        template.increment_usage()

        # Return template data for use
        serializer = self.get_serializer(template)
        return Response(serializer.data)
