"""
Customer Service Portal Models
Following DRY, ULM, CPR, GRS, VST, SMA, CBR, BOP, API-First Mentality principles
"""

from django.db import models
from django.conf import settings
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils import timezone
from djmoney.models.fields import MoneyField
from simple_history.models import HistoricalRecords
from datetime import datetime, timedelta
import uuid


class SLARule(models.Model):
    """SLA (Service Level Agreement) rules for ticket management"""

    class Priority(models.TextChoices):
        CRITICAL = "critical", "حرج"
        HIGH = "high", "عالي"
        MEDIUM = "medium", "متوسط"
        LOW = "low", "منخفض"

    class ClientTier(models.TextChoices):
        VIP = "vip", "VIP"
        PREMIUM = "premium", "مميز"
        STANDARD = "standard", "عادي"
        BASIC = "basic", "أساسي"

    # Rule Configuration
    name = models.CharField(max_length=100, verbose_name="اسم القاعدة")
    priority = models.CharField(
        max_length=10, choices=Priority.choices, verbose_name="الأولوية"
    )
    client_tier = models.CharField(
        max_length=10,
        choices=ClientTier.choices,
        default=ClientTier.STANDARD,
        verbose_name="فئة العميل",
    )

    # Time Limits (in hours)
    response_time_hours = models.PositiveIntegerField(
        verbose_name="وقت الاستجابة (ساعات)"
    )
    resolution_time_hours = models.PositiveIntegerField(verbose_name="وقت الحل (ساعات)")
    escalation_time_hours = models.PositiveIntegerField(
        verbose_name="وقت التصعيد (ساعات)"
    )

    # Configuration
    business_hours_only = models.BooleanField(
        default=True, verbose_name="ساعات العمل فقط"
    )
    is_active = models.BooleanField(default=True, verbose_name="نشط")

    # Tracking
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    # History tracking
    history = HistoricalRecords()

    class Meta:
        verbose_name = "قاعدة SLA"
        verbose_name_plural = "قواعد SLA"
        unique_together = ["priority", "client_tier"]
        ordering = ["priority", "client_tier"]

    def __str__(self):
        return f"{self.get_priority_display()} - {self.get_client_tier_display()}"


class KnowledgeBase(models.Model):
    """Knowledge base articles for self-service support"""

    class Category(models.TextChoices):
        GENERAL = "general", "عام"
        TECHNICAL = "technical", "تقني"
        BILLING = "billing", "الفواتير"
        PROJECTS = "projects", "المشاريع"
        ACCOUNT = "account", "الحساب"
        TROUBLESHOOTING = "troubleshooting", "استكشاف الأخطاء"

    # Content
    title_ar = models.CharField(max_length=200, verbose_name="العنوان بالعربية")
    title_en = models.CharField(
        max_length=200, blank=True, verbose_name="العنوان بالإنجليزية"
    )
    content_ar = models.TextField(verbose_name="المحتوى بالعربية")
    content_en = models.TextField(blank=True, verbose_name="المحتوى بالإنجليزية")

    # Categorization
    category = models.CharField(
        max_length=20,
        choices=Category.choices,
        default=Category.GENERAL,
        verbose_name="الفئة",
    )
    tags = models.JSONField(default=list, blank=True, verbose_name="العلامات")

    # Visibility
    is_public = models.BooleanField(default=True, verbose_name="عام")
    is_featured = models.BooleanField(default=False, verbose_name="مميز")

    # Analytics
    view_count = models.PositiveIntegerField(default=0, verbose_name="عدد المشاهدات")
    helpful_count = models.PositiveIntegerField(
        default=0, verbose_name="عدد التقييمات المفيدة"
    )
    not_helpful_count = models.PositiveIntegerField(
        default=0, verbose_name="عدد التقييمات غير المفيدة"
    )

    # Management
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        related_name="knowledge_articles",
        verbose_name="أنشأ بواسطة",
    )
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    # History tracking
    history = HistoricalRecords()

    class Meta:
        verbose_name = "مقال قاعدة المعرفة"
        verbose_name_plural = "مقالات قاعدة المعرفة"
        ordering = ["-is_featured", "-created_at"]
        indexes = [
            models.Index(
                fields=["category", "is_public"], name="kb_category_public_idx"
            ),
            models.Index(
                fields=["is_featured", "is_public"], name="kb_featured_public_idx"
            ),
        ]

    def __str__(self):
        return self.title_ar

    @property
    def helpfulness_ratio(self):
        """Calculate helpfulness ratio"""
        total_votes = self.helpful_count + self.not_helpful_count
        if total_votes == 0:
            return 0
        return (self.helpful_count / total_votes) * 100


class Ticket(models.Model):
    """Support ticket model for customer service management"""

    class Status(models.TextChoices):
        OPEN = "open", "مفتوح"
        IN_PROGRESS = "in_progress", "قيد المعالجة"
        PENDING_CLIENT = "pending_client", "في انتظار العميل"
        RESOLVED = "resolved", "تم الحل"
        CLOSED = "closed", "مغلق"
        ESCALATED = "escalated", "تم التصعيد"

    class Priority(models.TextChoices):
        CRITICAL = "critical", "حرج"
        HIGH = "high", "عالي"
        MEDIUM = "medium", "متوسط"
        LOW = "low", "منخفض"

    class Category(models.TextChoices):
        TECHNICAL = "technical", "تقني"
        BILLING = "billing", "الفواتير"
        GENERAL = "general", "عام"
        PROJECT_RELATED = "project_related", "متعلق بالمشروع"
        ACCOUNT = "account", "الحساب"
        FEATURE_REQUEST = "feature_request", "طلب ميزة"
        BUG_REPORT = "bug_report", "تقرير خطأ"

    class Source(models.TextChoices):
        EMAIL = "email", "بريد إلكتروني"
        PHONE = "phone", "هاتف"
        WHATSAPP = "whatsapp", "واتساب"
        WEB_PORTAL = "web_portal", "البوابة الإلكترونية"
        INTERNAL = "internal", "داخلي"

    # Basic Information
    ticket_number = models.CharField(
        max_length=20, unique=True, verbose_name="رقم التذكرة"
    )
    title = models.CharField(max_length=200, verbose_name="العنوان")
    description = models.TextField(verbose_name="الوصف")

    # Relationships
    client = models.ForeignKey(
        "clients.Client",
        on_delete=models.CASCADE,
        related_name="support_tickets",
        verbose_name="العميل",
    )
    project = models.ForeignKey(
        "projects.Project",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="support_tickets",
        verbose_name="المشروع",
    )
    assigned_to = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="assigned_tickets",
        verbose_name="مكلف إلى",
    )
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        related_name="created_tickets",
        verbose_name="أنشأ بواسطة",
    )

    # Classification
    status = models.CharField(
        max_length=20,
        choices=Status.choices,
        default=Status.OPEN,
        verbose_name="الحالة",
    )
    priority = models.CharField(
        max_length=10,
        choices=Priority.choices,
        default=Priority.MEDIUM,
        verbose_name="الأولوية",
    )
    category = models.CharField(
        max_length=20,
        choices=Category.choices,
        default=Category.GENERAL,
        verbose_name="الفئة",
    )
    source = models.CharField(
        max_length=15,
        choices=Source.choices,
        default=Source.WEB_PORTAL,
        verbose_name="المصدر",
    )

    # SLA Tracking
    sla_due_date = models.DateTimeField(null=True, blank=True, verbose_name="موعد SLA")
    escalation_level = models.PositiveIntegerField(
        default=1, verbose_name="مستوى التصعيد"
    )

    # Timeline
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")
    resolved_at = models.DateTimeField(null=True, blank=True, verbose_name="تاريخ الحل")
    closed_at = models.DateTimeField(
        null=True, blank=True, verbose_name="تاريخ الإغلاق"
    )

    # Client Satisfaction
    satisfaction_rating = models.PositiveIntegerField(
        null=True,
        blank=True,
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        verbose_name="تقييم الرضا",
    )
    satisfaction_feedback = models.TextField(
        blank=True, null=True, verbose_name="تعليق الرضا"
    )

    # Additional Data
    attachments = models.JSONField(default=list, blank=True, verbose_name="المرفقات")
    metadata = models.JSONField(default=dict, blank=True, verbose_name="بيانات إضافية")

    # History tracking
    history = HistoricalRecords()

    class Meta:
        verbose_name = "تذكرة دعم"
        verbose_name_plural = "تذاكر الدعم"
        ordering = ["-created_at"]
        indexes = [
            models.Index(
                fields=["status", "priority"], name="ticket_status_priority_idx"
            ),
            models.Index(fields=["client", "status"], name="ticket_client_status_idx"),
            models.Index(
                fields=["assigned_to", "status"], name="ticket_assigned_status_idx"
            ),
            models.Index(fields=["sla_due_date"], name="ticket_sla_due_idx"),
        ]

    def __str__(self):
        return f"{self.ticket_number} - {self.title}"

    def save(self, *args, **kwargs):
        """Override save to generate ticket number and set SLA"""
        if not self.ticket_number:
            self.ticket_number = self.generate_ticket_number()

        # Set SLA due date if not set
        if not self.sla_due_date and self.status == self.Status.OPEN:
            self.set_sla_due_date()

        super().save(*args, **kwargs)

    def generate_ticket_number(self):
        """Generate unique ticket number"""
        today = timezone.now()
        prefix = f"TK{today.strftime('%Y%m%d')}"

        # Get last ticket number for today
        last_ticket = (
            Ticket.objects.filter(ticket_number__startswith=prefix)
            .order_by("-ticket_number")
            .first()
        )

        if last_ticket:
            last_number = int(last_ticket.ticket_number[-4:])
            new_number = last_number + 1
        else:
            new_number = 1

        return f"{prefix}{new_number:04d}"

    def set_sla_due_date(self):
        """Set SLA due date based on priority and client tier"""
        try:
            # Get client tier (assuming it's stored in client model)
            client_tier = getattr(self.client, "tier", "standard")

            # Get SLA rule
            sla_rule = SLARule.objects.filter(
                priority=self.priority, client_tier=client_tier, is_active=True
            ).first()

            if sla_rule:
                hours_to_add = sla_rule.response_time_hours
                self.sla_due_date = timezone.now() + timedelta(hours=hours_to_add)
        except Exception:
            # Fallback to default SLA
            default_hours = {
                self.Priority.CRITICAL: 1,
                self.Priority.HIGH: 4,
                self.Priority.MEDIUM: 24,
                self.Priority.LOW: 48,
            }
            hours = default_hours.get(self.priority, 24)
            self.sla_due_date = timezone.now() + timedelta(hours=hours)

    @property
    def is_overdue(self):
        """Check if ticket is overdue"""
        if not self.sla_due_date:
            return False
        return timezone.now() > self.sla_due_date and self.status not in [
            self.Status.RESOLVED,
            self.Status.CLOSED,
        ]

    @property
    def time_to_resolution(self):
        """Calculate time to resolution"""
        if self.resolved_at:
            return self.resolved_at - self.created_at
        return None

    @property
    def response_time(self):
        """Calculate first response time"""
        first_response = (
            self.responses.filter(is_internal=False).order_by("created_at").first()
        )

        if first_response:
            return first_response.created_at - self.created_at
        return None


class TicketResponse(models.Model):
    """Responses and communications for support tickets"""

    class ResponseType(models.TextChoices):
        COMMENT = "comment", "تعليق"
        STATUS_CHANGE = "status_change", "تغيير الحالة"
        ASSIGNMENT = "assignment", "تكليف"
        ESCALATION = "escalation", "تصعيد"
        RESOLUTION = "resolution", "حل"
        CLIENT_FEEDBACK = "client_feedback", "تعليق العميل"

    # Relationships
    ticket = models.ForeignKey(
        Ticket,
        on_delete=models.CASCADE,
        related_name="responses",
        verbose_name="التذكرة",
    )
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        related_name="ticket_responses",
        verbose_name="المستخدم",
    )

    # Content
    response_text = models.TextField(verbose_name="نص الرد")
    response_type = models.CharField(
        max_length=20,
        choices=ResponseType.choices,
        default=ResponseType.COMMENT,
        verbose_name="نوع الرد",
    )

    # Visibility
    is_internal = models.BooleanField(default=False, verbose_name="داخلي")

    # Attachments
    attachments = models.JSONField(default=list, blank=True, verbose_name="المرفقات")

    # Tracking
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    # History tracking
    history = HistoricalRecords()

    class Meta:
        verbose_name = "رد التذكرة"
        verbose_name_plural = "ردود التذاكر"
        ordering = ["created_at"]
        indexes = [
            models.Index(
                fields=["ticket", "created_at"], name="response_ticket_created_idx"
            ),
            models.Index(
                fields=["user", "created_at"], name="response_user_created_idx"
            ),
        ]

    def __str__(self):
        return f"رد على {self.ticket.ticket_number} - {self.user}"


class TicketEscalation(models.Model):
    """Ticket escalation tracking"""

    class EscalationReason(models.TextChoices):
        SLA_BREACH = "sla_breach", "انتهاك SLA"
        CLIENT_REQUEST = "client_request", "طلب العميل"
        COMPLEXITY = "complexity", "تعقيد"
        MANUAL = "manual", "يدوي"

    # Relationships
    ticket = models.ForeignKey(
        Ticket,
        on_delete=models.CASCADE,
        related_name="escalations",
        verbose_name="التذكرة",
    )
    escalated_from = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        related_name="escalated_from_tickets",
        verbose_name="تم التصعيد من",
    )
    escalated_to = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        related_name="escalated_to_tickets",
        verbose_name="تم التصعيد إلى",
    )
    escalated_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        related_name="escalated_by_tickets",
        verbose_name="تم التصعيد بواسطة",
    )

    # Escalation Details
    reason = models.CharField(
        max_length=20, choices=EscalationReason.choices, verbose_name="سبب التصعيد"
    )
    notes = models.TextField(blank=True, verbose_name="ملاحظات")
    escalation_level = models.PositiveIntegerField(verbose_name="مستوى التصعيد")

    # Tracking
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ التصعيد")

    # History tracking
    history = HistoricalRecords()

    class Meta:
        verbose_name = "تصعيد التذكرة"
        verbose_name_plural = "تصعيدات التذاكر"
        ordering = ["-created_at"]

    def __str__(self):
        return f"تصعيد {self.ticket.ticket_number} - المستوى {self.escalation_level}"


class TicketTemplate(models.Model):
    """Predefined templates for common ticket responses"""

    class TemplateType(models.TextChoices):
        RESPONSE = "response", "رد"
        RESOLUTION = "resolution", "حل"
        ESCALATION = "escalation", "تصعيد"
        CLOSURE = "closure", "إغلاق"

    # Template Information
    name = models.CharField(max_length=100, verbose_name="اسم القالب")
    description = models.TextField(blank=True, verbose_name="الوصف")
    template_type = models.CharField(
        max_length=15, choices=TemplateType.choices, verbose_name="نوع القالب"
    )

    # Content
    subject_template = models.CharField(
        max_length=200, blank=True, verbose_name="قالب الموضوع"
    )
    body_template = models.TextField(verbose_name="قالب المحتوى")

    # Configuration
    category = models.CharField(
        max_length=20, choices=Ticket.Category.choices, blank=True, verbose_name="الفئة"
    )
    is_active = models.BooleanField(default=True, verbose_name="نشط")

    # Usage Statistics
    usage_count = models.PositiveIntegerField(default=0, verbose_name="عدد الاستخدامات")

    # Management
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        verbose_name="أنشأ بواسطة",
    )
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    # History tracking
    history = HistoricalRecords()

    class Meta:
        verbose_name = "قالب التذكرة"
        verbose_name_plural = "قوالب التذاكر"
        ordering = ["template_type", "name"]

    def __str__(self):
        return f"{self.name} ({self.get_template_type_display()})"

    def increment_usage(self):
        """Increment usage count"""
        self.usage_count += 1
        self.save(update_fields=["usage_count"])
