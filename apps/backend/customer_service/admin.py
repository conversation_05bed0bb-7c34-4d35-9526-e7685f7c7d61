"""
Customer Service Portal Admin Configuration
Following DRY, ULM, CPR, GRS, VST, SMA, CBR, BOP, API-First Mentality principles
"""

from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe
from .models import (
    SLARule,
    KnowledgeBase,
    Ticket,
    TicketResponse,
    TicketEscalation,
    TicketTemplate
)


@admin.register(SLARule)
class SLARuleAdmin(admin.ModelAdmin):
    """SLA Rule admin configuration"""
    list_display = [
        'name', 'priority', 'client_tier', 'response_time_hours',
        'resolution_time_hours', 'escalation_time_hours', 'is_active'
    ]
    list_filter = ['priority', 'client_tier', 'is_active', 'business_hours_only']
    search_fields = ['name']
    ordering = ['priority', 'client_tier']
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'priority', 'client_tier')
        }),
        ('Time Limits', {
            'fields': ('response_time_hours', 'resolution_time_hours', 'escalation_time_hours')
        }),
        ('Configuration', {
            'fields': ('business_hours_only', 'is_active')
        }),
    )


@admin.register(KnowledgeBase)
class KnowledgeBaseAdmin(admin.ModelAdmin):
    """Knowledge Base admin configuration"""
    list_display = [
        'title_ar', 'category', 'is_public', 'is_featured',
        'view_count', 'helpful_count', 'helpfulness_ratio', 'created_at'
    ]
    list_filter = ['category', 'is_public', 'is_featured', 'created_at']
    search_fields = ['title_ar', 'title_en', 'content_ar', 'content_en']
    ordering = ['-is_featured', '-created_at']
    readonly_fields = ['view_count', 'helpful_count', 'not_helpful_count', 'created_at', 'updated_at']
    
    fieldsets = (
        ('Content', {
            'fields': ('title_ar', 'title_en', 'content_ar', 'content_en')
        }),
        ('Categorization', {
            'fields': ('category', 'tags')
        }),
        ('Visibility', {
            'fields': ('is_public', 'is_featured')
        }),
        ('Analytics', {
            'fields': ('view_count', 'helpful_count', 'not_helpful_count'),
            'classes': ('collapse',)
        }),
        ('Management', {
            'fields': ('created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def save_model(self, request, obj, form, change):
        """Set created_by on save"""
        if not change:
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


class TicketResponseInline(admin.TabularInline):
    """Inline for ticket responses"""
    model = TicketResponse
    extra = 0
    readonly_fields = ['user', 'created_at', 'updated_at']
    fields = ['response_text', 'response_type', 'is_internal', 'user', 'created_at']
    
    def get_queryset(self, request):
        """Order responses by creation date"""
        return super().get_queryset(request).order_by('created_at')


class TicketEscalationInline(admin.TabularInline):
    """Inline for ticket escalations"""
    model = TicketEscalation
    extra = 0
    readonly_fields = ['escalated_by', 'created_at']
    fields = ['reason', 'escalated_from', 'escalated_to', 'escalated_by', 'escalation_level', 'created_at']


@admin.register(Ticket)
class TicketAdmin(admin.ModelAdmin):
    """Ticket admin configuration"""
    list_display = [
        'ticket_number', 'title', 'client', 'status', 'priority',
        'assigned_to', 'sla_status', 'created_at'
    ]
    list_filter = [
        'status', 'priority', 'category', 'source',
        'created_at', 'sla_due_date'
    ]
    search_fields = [
        'ticket_number', 'title', 'description',
        'client__name', 'client__email'
    ]
    ordering = ['-created_at']
    readonly_fields = [
        'ticket_number', 'sla_due_date', 'escalation_level',
        'created_at', 'updated_at', 'resolved_at', 'closed_at'
    ]
    inlines = [TicketResponseInline, TicketEscalationInline]
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('ticket_number', 'title', 'description')
        }),
        ('Relationships', {
            'fields': ('client', 'project', 'assigned_to', 'created_by')
        }),
        ('Classification', {
            'fields': ('status', 'priority', 'category', 'source')
        }),
        ('SLA & Escalation', {
            'fields': ('sla_due_date', 'escalation_level'),
            'classes': ('collapse',)
        }),
        ('Timeline', {
            'fields': ('created_at', 'updated_at', 'resolved_at', 'closed_at'),
            'classes': ('collapse',)
        }),
        ('Client Satisfaction', {
            'fields': ('satisfaction_rating', 'satisfaction_feedback'),
            'classes': ('collapse',)
        }),
        ('Additional Data', {
            'fields': ('attachments', 'metadata'),
            'classes': ('collapse',)
        }),
    )
    
    def sla_status(self, obj):
        """Display SLA status with color coding"""
        if obj.is_overdue:
            return format_html(
                '<span style="color: red; font-weight: bold;">متأخر</span>'
            )
        elif obj.sla_due_date:
            from django.utils import timezone
            time_left = obj.sla_due_date - timezone.now()
            if time_left.total_seconds() < 3600:  # Less than 1 hour
                return format_html(
                    '<span style="color: orange; font-weight: bold;">قريب الانتهاء</span>'
                )
            else:
                return format_html(
                    '<span style="color: green;">في الوقت المحدد</span>'
                )
        return '-'
    sla_status.short_description = 'حالة SLA'
    
    def save_model(self, request, obj, form, change):
        """Set created_by on save"""
        if not change:
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(TicketResponse)
class TicketResponseAdmin(admin.ModelAdmin):
    """Ticket Response admin configuration"""
    list_display = [
        'ticket', 'user', 'response_type', 'is_internal', 'created_at'
    ]
    list_filter = ['response_type', 'is_internal', 'created_at']
    search_fields = ['response_text', 'ticket__ticket_number', 'user__username']
    ordering = ['-created_at']
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = (
        ('Response Information', {
            'fields': ('ticket', 'user', 'response_text', 'response_type')
        }),
        ('Configuration', {
            'fields': ('is_internal', 'attachments')
        }),
        ('Timeline', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(TicketEscalation)
class TicketEscalationAdmin(admin.ModelAdmin):
    """Ticket Escalation admin configuration"""
    list_display = [
        'ticket', 'reason', 'escalated_from', 'escalated_to',
        'escalation_level', 'created_at'
    ]
    list_filter = ['reason', 'escalation_level', 'created_at']
    search_fields = ['ticket__ticket_number', 'notes']
    ordering = ['-created_at']
    readonly_fields = ['created_at']
    
    fieldsets = (
        ('Escalation Information', {
            'fields': ('ticket', 'reason', 'notes', 'escalation_level')
        }),
        ('Users', {
            'fields': ('escalated_from', 'escalated_to', 'escalated_by')
        }),
        ('Timeline', {
            'fields': ('created_at',)
        }),
    )


@admin.register(TicketTemplate)
class TicketTemplateAdmin(admin.ModelAdmin):
    """Ticket Template admin configuration"""
    list_display = [
        'name', 'template_type', 'category', 'is_active',
        'usage_count', 'created_at'
    ]
    list_filter = ['template_type', 'category', 'is_active', 'created_at']
    search_fields = ['name', 'description', 'body_template']
    ordering = ['template_type', 'name']
    readonly_fields = ['usage_count', 'created_at', 'updated_at']
    
    fieldsets = (
        ('Template Information', {
            'fields': ('name', 'description', 'template_type', 'category')
        }),
        ('Content', {
            'fields': ('subject_template', 'body_template')
        }),
        ('Configuration', {
            'fields': ('is_active',)
        }),
        ('Statistics', {
            'fields': ('usage_count',),
            'classes': ('collapse',)
        }),
        ('Management', {
            'fields': ('created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def save_model(self, request, obj, form, change):
        """Set created_by on save"""
        if not change:
            obj.created_by = request.user
        super().save_model(request, obj, form, change)
