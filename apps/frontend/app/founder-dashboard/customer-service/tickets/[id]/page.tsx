'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import {
  AlertTriangle,
  ArrowLeft,
  CheckCircle,
  Clock,
  MessageSquare,
  User,
  Calendar,
  Building,
  Phone,
  Mail,
  FileText,
  Send,
  UserPlus,
  TrendingUp,
  XCircle,
  RefreshCw,
  Star
} from 'lucide-react';
import { UnifiedLayout } from '@/components/layout';
import { customerServiceAPI, type Ticket, type TicketResponse } from '@/lib/api/customer-service';
import { toast } from 'sonner';
import { formatRelativeTime, formatDateTime } from '@mtbrmg/shared';

const TicketDetailsPage = () => {
  const router = useRouter();
  const params = useParams();
  const ticketId = parseInt(params.id as string);
  
  const [loading, setLoading] = useState(true);
  const [ticket, setTicket] = useState<Ticket | null>(null);
  const [responses, setResponses] = useState<TicketResponse[]>([]);
  const [newResponse, setNewResponse] = useState('');
  const [responseType, setResponseType] = useState<'comment' | 'resolution'>('comment');
  const [submittingResponse, setSubmittingResponse] = useState(false);

  useEffect(() => {
    if (ticketId) {
      loadTicketDetails();
    }
  }, [ticketId]);

  const loadTicketDetails = async () => {
    try {
      setLoading(true);
      const response = await customerServiceAPI.getTicket(ticketId);
      setTicket(response.data);
      setResponses(response.data.responses || []);
    } catch (error) {
      console.error('Error loading ticket details:', error);
      toast.error('فشل في تحميل تفاصيل التذكرة');
      router.push('/founder-dashboard/customer-service');
    } finally {
      setLoading(false);
    }
  };

  const handleAddResponse = async () => {
    if (!newResponse.trim() || !ticket) return;

    try {
      setSubmittingResponse(true);
      
      if (responseType === 'resolution') {
        await customerServiceAPI.resolveTicket(ticket.id, newResponse);
        toast.success('تم حل التذكرة بنجاح');
      } else {
        await customerServiceAPI.addTicketResponse(ticket.id, {
          response_text: newResponse,
          response_type: responseType,
          is_internal: false
        });
        toast.success('تم إضافة الرد بنجاح');
      }
      
      setNewResponse('');
      await loadTicketDetails();
    } catch (error) {
      console.error('Error adding response:', error);
      toast.error('فشل في إضافة الرد');
    } finally {
      setSubmittingResponse(false);
    }
  };

  const handleStatusChange = async (newStatus: string) => {
    if (!ticket) return;

    try {
      await customerServiceAPI.updateTicket(ticket.id, { status: newStatus });
      toast.success('تم تحديث حالة التذكرة');
      await loadTicketDetails();
    } catch (error) {
      console.error('Error updating ticket status:', error);
      toast.error('فشل في تحديث حالة التذكرة');
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      open: { color: 'bg-blue-100 text-blue-800', icon: Clock, label: 'مفتوح' },
      in_progress: { color: 'bg-yellow-100 text-yellow-800', icon: RefreshCw, label: 'قيد المعالجة' },
      pending_client: { color: 'bg-orange-100 text-orange-800', icon: MessageSquare, label: 'في انتظار العميل' },
      resolved: { color: 'bg-green-100 text-green-800', icon: CheckCircle, label: 'تم الحل' },
      closed: { color: 'bg-gray-100 text-gray-800', icon: XCircle, label: 'مغلق' },
      escalated: { color: 'bg-red-100 text-red-800', icon: AlertTriangle, label: 'تم التصعيد' },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.open;
    const Icon = config.icon;

    return (
      <Badge className={`${config.color} flex items-center gap-1`}>
        <Icon className="h-3 w-3" />
        {config.label}
      </Badge>
    );
  };

  const getPriorityBadge = (priority: string) => {
    const priorityConfig = {
      critical: { color: 'bg-red-100 text-red-800 border-red-200', label: 'حرج' },
      high: { color: 'bg-orange-100 text-orange-800 border-orange-200', label: 'عالي' },
      medium: { color: 'bg-yellow-100 text-yellow-800 border-yellow-200', label: 'متوسط' },
      low: { color: 'bg-green-100 text-green-800 border-green-200', label: 'منخفض' },
    };

    const config = priorityConfig[priority as keyof typeof priorityConfig] || priorityConfig.medium;

    return (
      <Badge variant="outline" className={config.color}>
        {config.label}
      </Badge>
    );
  };

  if (loading) {
    return (
      <UnifiedLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-600" />
            <p className="text-gray-600">جاري تحميل تفاصيل التذكرة...</p>
          </div>
        </div>
      </UnifiedLayout>
    );
  }

  if (!ticket) {
    return (
      <UnifiedLayout>
        <div className="text-center py-12">
          <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">التذكرة غير موجودة</h3>
          <p className="text-gray-600 mb-4">لم يتم العثور على التذكرة المطلوبة</p>
          <Button onClick={() => router.push('/founder-dashboard/customer-service')}>
            العودة إلى خدمة العملاء
          </Button>
        </div>
      </UnifiedLayout>
    );
  }

  return (
    <UnifiedLayout>
      <div className="max-w-6xl mx-auto space-y-6">
        {/* Header Section */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button
              variant="outline"
              size="sm"
              onClick={() => router.push('/founder-dashboard/customer-service')}
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              العودة
            </Button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">{ticket.title}</h1>
              <p className="text-gray-600">رقم التذكرة: {ticket.ticket_number}</p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            {getStatusBadge(ticket.status)}
            {getPriorityBadge(ticket.priority)}
            {ticket.is_overdue && (
              <Badge className="bg-red-100 text-red-800">
                <AlertTriangle className="h-3 w-3 mr-1" />
                متأخر
              </Badge>
            )}
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Ticket Details */}
            <Card>
              <CardHeader>
                <CardTitle>تفاصيل التذكرة</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">الوصف</h4>
                  <p className="text-gray-700 whitespace-pre-wrap">{ticket.description}</p>
                </div>
                
                <Separator />
                
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium text-gray-600">الفئة:</span>
                    <span className="ml-2">{ticket.category_display}</span>
                  </div>
                  <div>
                    <span className="font-medium text-gray-600">المصدر:</span>
                    <span className="ml-2">{ticket.source_display}</span>
                  </div>
                  <div>
                    <span className="font-medium text-gray-600">تاريخ الإنشاء:</span>
                    <span className="ml-2">{formatDateTime(ticket.created_at)}</span>
                  </div>
                  {ticket.sla_due_date && (
                    <div>
                      <span className="font-medium text-gray-600">موعد SLA:</span>
                      <span className="ml-2">{formatDateTime(ticket.sla_due_date)}</span>
                    </div>
                  )}
                </div>

                {ticket.satisfaction_rating && (
                  <>
                    <Separator />
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">تقييم العميل</h4>
                      <div className="flex items-center gap-2 mb-2">
                        {[1, 2, 3, 4, 5].map((star) => (
                          <Star
                            key={star}
                            className={`h-4 w-4 ${
                              star <= ticket.satisfaction_rating!
                                ? 'text-yellow-400 fill-current'
                                : 'text-gray-300'
                            }`}
                          />
                        ))}
                        <span className="text-sm text-gray-600">
                          ({ticket.satisfaction_rating}/5)
                        </span>
                      </div>
                      {ticket.satisfaction_feedback && (
                        <p className="text-gray-700 text-sm">{ticket.satisfaction_feedback}</p>
                      )}
                    </div>
                  </>
                )}
              </CardContent>
            </Card>

            {/* Responses */}
            <Card>
              <CardHeader>
                <CardTitle>الردود والتحديثات</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {responses.length === 0 ? (
                    <p className="text-gray-600 text-center py-4">لا توجد ردود بعد</p>
                  ) : (
                    responses.map((response) => (
                      <div key={response.id} className="border rounded-lg p-4">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center gap-2">
                            <User className="h-4 w-4 text-gray-400" />
                            <span className="font-medium text-gray-900">
                              {response.user?.full_name || 'النظام'}
                            </span>
                            <Badge variant="outline" className="text-xs">
                              {response.response_type_display}
                            </Badge>
                            {response.is_internal && (
                              <Badge variant="outline" className="text-xs bg-yellow-50 text-yellow-700">
                                داخلي
                              </Badge>
                            )}
                          </div>
                          <span className="text-sm text-gray-500">
                            {formatRelativeTime(response.created_at)}
                          </span>
                        </div>
                        <p className="text-gray-700 whitespace-pre-wrap">{response.response_text}</p>
                      </div>
                    ))
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Add Response */}
            {ticket.status !== 'closed' && (
              <Card>
                <CardHeader>
                  <CardTitle>إضافة رد</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex gap-2">
                    <Select value={responseType} onValueChange={(value: 'comment' | 'resolution') => setResponseType(value)}>
                      <SelectTrigger className="w-48">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="comment">تعليق</SelectItem>
                        <SelectItem value="resolution">حل التذكرة</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <Textarea
                    placeholder="اكتب ردك هنا..."
                    value={newResponse}
                    onChange={(e) => setNewResponse(e.target.value)}
                    rows={4}
                  />
                  
                  <Button 
                    onClick={handleAddResponse}
                    disabled={!newResponse.trim() || submittingResponse}
                    className="bg-blue-600 hover:bg-blue-700"
                  >
                    {submittingResponse ? (
                      <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    ) : (
                      <Send className="h-4 w-4 mr-2" />
                    )}
                    {responseType === 'resolution' ? 'حل التذكرة' : 'إرسال الرد'}
                  </Button>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Client Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="h-5 w-5" />
                  معلومات العميل
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-center gap-2">
                  <Building className="h-4 w-4 text-gray-400" />
                  <div>
                    <p className="font-medium">{ticket.client.name}</p>
                    {ticket.client.company && (
                      <p className="text-sm text-gray-600">{ticket.client.company}</p>
                    )}
                  </div>
                </div>
                
                <div className="flex items-center gap-2">
                  <Mail className="h-4 w-4 text-gray-400" />
                  <span className="text-sm">{ticket.client.email}</span>
                </div>
                
                <div className="flex items-center gap-2">
                  <Phone className="h-4 w-4 text-gray-400" />
                  <span className="text-sm">{ticket.client.phone}</span>
                </div>
              </CardContent>
            </Card>

            {/* Project Information */}
            {ticket.project && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="h-5 w-5" />
                    معلومات المشروع
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div>
                    <p className="font-medium">{ticket.project.name}</p>
                    <p className="text-sm text-gray-600">{ticket.project.type}</p>
                    <Badge variant="outline" className="mt-2">
                      {ticket.project.status}
                    </Badge>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Assignment */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <UserPlus className="h-5 w-5" />
                  التكليف
                </CardTitle>
              </CardHeader>
              <CardContent>
                {ticket.assigned_to ? (
                  <div>
                    <p className="font-medium">{ticket.assigned_to.full_name}</p>
                    <p className="text-sm text-gray-600">{ticket.assigned_to.role}</p>
                  </div>
                ) : (
                  <p className="text-gray-600">غير مكلف</p>
                )}
              </CardContent>
            </Card>

            {/* Actions */}
            <Card>
              <CardHeader>
                <CardTitle>الإجراءات</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                {ticket.status === 'open' && (
                  <Button 
                    variant="outline" 
                    className="w-full"
                    onClick={() => handleStatusChange('in_progress')}
                  >
                    بدء المعالجة
                  </Button>
                )}
                
                {ticket.status === 'in_progress' && (
                  <Button 
                    variant="outline" 
                    className="w-full"
                    onClick={() => handleStatusChange('pending_client')}
                  >
                    في انتظار العميل
                  </Button>
                )}
                
                {ticket.status !== 'closed' && ticket.status !== 'resolved' && (
                  <Button 
                    variant="outline" 
                    className="w-full"
                    onClick={() => handleStatusChange('resolved')}
                  >
                    <CheckCircle className="h-4 w-4 mr-2" />
                    حل التذكرة
                  </Button>
                )}
                
                {ticket.status === 'resolved' && (
                  <Button 
                    variant="outline" 
                    className="w-full"
                    onClick={() => handleStatusChange('closed')}
                  >
                    <XCircle className="h-4 w-4 mr-2" />
                    إغلاق التذكرة
                  </Button>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </UnifiedLayout>
  );
};

export default TicketDetailsPage;
